name: Release Application

on:
    push:
        tags:
            - 'v*'

jobs:
    build-and-publish:
        name: Build and Publish with Velopack
        runs-on: ${{ matrix.os }}
        strategy:
            matrix:
                include:
                    - os: windows-latest
                      runtime: win-x64
                    - os: ubuntu-latest
                      runtime: linux-x64
                    - os: macos-latest
                      runtime: osx-x64
        
        steps:
            - name: Checkout Repository
              uses: actions/checkout@v4
              with:
                  submodules: recursive

            - name: Setup .NET SDK
              uses: actions/setup-dotnet@v4
              with:
                  version: 9.0.x

            - name: Install Velopack CLI
              run: dotnet tool install -g vpk

            - name: Publish Application
              run: |
                  dotnet publish -c Release --self-contained -r ${{ matrix.runtime }} src/Polymerium.App/Polymerium.App.csproj

            - name: Extract version from tag
              id: get_version
              run: |
                  $version = "${{ github.ref_name }}" -replace '^v', ''
                  echo "version=$version" >> $env:GITHUB_OUTPUT

            - name: Pack with Velopack
              run: |
                  vpk pack --packId Polymerium --packVersion ${{ steps.get_version.outputs.version }} --packDir src/Polymerium.App/bin/Release/net9.0/${{ matrix.runtime }}/publish --mainExe Polymerium.App

            - name: Upload Release Assets
              uses: actions/upload-artifact@v4
              with:
                  name: polymerium-release-${{ matrix.runtime }}-${{ steps.get_version.outputs.version }}
                  path: releases/
