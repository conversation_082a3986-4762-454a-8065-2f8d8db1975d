name: Release Application

on:
    push:
        tags:
            - 'v*'

jobs:
    build-and-publish:
        name: Build and Publish with Velopack
        runs-on: ${{ matrix.os }}
        strategy:
            matrix:
                include:
                    - os: windows-latest
                      runtime: win-x64
                    - os: ubuntu-latest
                      runtime: linux-x64
                    # - os: macos-latest
                    #   runtime: osx-x64
        
        steps:
            - name: Checkout Repository
              uses: actions/checkout@v4
              with:
                  submodules: recursive

            - name: Setup .NET SDK
              uses: actions/setup-dotnet@v4
              with:
                  version: 9.0.x

            - name: Install Velopack CLI
              run: dotnet tool install -g vpk

            - name: Publish Application
              run: |
                  dotnet publish -c Release --self-contained -r ${{ matrix.runtime }} src/Polymerium.App/Polymerium.App.csproj

            - name: Extract version from tag and determine release type
              id: get_version
              run: |
                  $version = "${{ github.ref_name }}" -replace '^v', ''
                  echo "version=$version" >> $env:GITHUB_OUTPUT

                  # Check if version contains metadata (+ character)
                  if ($version -match '\+') {
                      echo "is_prerelease=true" >> $env:GITHUB_OUTPUT
                      echo "release_name=v$version (Preview)" >> $env:GITHUB_OUTPUT
                  } else {
                      echo "is_prerelease=false" >> $env:GITHUB_OUTPUT
                      echo "release_name=v$version" >> $env:GITHUB_OUTPUT
                  }

            - name: Pack with Velopack
              run: |
                  vpk pack --packId Polymerium --packVersion ${{ steps.get_version.outputs.version }} --packDir src/Polymerium.App/bin/Release/net9.0/${{ matrix.runtime }}/publish --mainExe Polymerium.App.exe

            - name: Upload to GitHub Releases
              env:
                  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
              run: |
                  $preFlag = if ("${{ steps.get_version.outputs.is_prerelease }}" -eq "true") { "--pre" } else { "" }
                  vpk upload github --repoUrl ${{ github.repository }} --publish $preFlag --releaseName "${{ steps.get_version.outputs.release_name }}" --tag "v${{ steps.get_version.outputs.version }}" --token ${{ secrets.GITHUB_TOKEN }}
