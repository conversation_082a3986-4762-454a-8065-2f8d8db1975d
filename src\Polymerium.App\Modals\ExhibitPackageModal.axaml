﻿<husk:Modal xmlns="https://github.com/avaloniaui"
            xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
            xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
            xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
            xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
            xmlns:m="clr-namespace:Polymerium.App.Models"
            xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
            xmlns:modals="clr-namespace:Polymerium.App.Modals"
            xmlns:trident="clr-namespace:Trident.Abstractions.Repositories.Resources;assembly=Trident.Abstractions"
            xmlns:async="clr-namespace:AsyncImageLoader;assembly=AsyncImageLoader.Avalonia"
            xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
            xmlns:controls="clr-namespace:Polymerium.App.Controls"
            xmlns:mdxaml="https://github.com/whistyun/Markdown.Avalonia"
            xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
            xmlns:md="https://github.com/whistyun/Markdown.Avalonia.Tight"
            mc:Ignorable="d" d:DesignWidth="720" d:DesignHeight="660" MinHeight="360"
            x:Class="Polymerium.App.Modals.ExhibitPackageModal" x:DataType="m:ExhibitPackageModel"
            Background="{x:Null}" BorderBrush="{x:Null}"
            BorderThickness="0" Padding="0" CornerRadius="0" Margin="64" VerticalAlignment="Stretch">
    <husk:Modal.Resources>
        <DataTemplate x:Key="VersionEditorTemplate">
            <Border>
                <Grid RowDefinitions="Auto,Auto" RowSpacing="4">
                    <Border Grid.Row="0"
                            Background="{StaticResource ControlTranslucentHalfBackgroundBrush}"
                            CornerRadius="{StaticResource SmallCornerRadius}"
                            Padding="12,8">
                        <CheckBox
                            IsChecked="{Binding $parent[modals:ExhibitPackageModal].IsFilterEnabled,Mode=TwoWay}">
                            <TextBlock Text="{x:Static lang:Resources.ExhibitPackageModal_FilterLabelText}" />
                        </CheckBox>
                    </Border>
                    <TabStrip Grid.Row="1"
                              SelectedIndex="{Binding $parent[modals:ExhibitPackageModal].SelectedVersionMode,Mode=TwoWay}">
                        <TabStrip.ItemsPanel>
                            <ItemsPanelTemplate>
                                <Grid ColumnDefinitions="*,Auto" ColumnSpacing="8" />
                            </ItemsPanelTemplate>
                        </TabStrip.ItemsPanel>
                        <TabStripItem Grid.Column="0" Padding="0">
                            <husk:LazyContainer
                                Source="{Binding $parent[modals:ExhibitPackageModal].LazyVersions,Mode=OneWay}"
                                CornerRadius="{StaticResource SmallCornerRadius}">
                                <husk:LazyContainer.SourceTemplate>
                                    <DataTemplate DataType="m:ExhibitVersionCollection">
                                        <ComboBox Theme="{StaticResource GhostComboBoxTheme}"
                                                  SelectedItem="{Binding $parent[modals:ExhibitPackageModal].SelectedVersion,Mode=TwoWay,FallbackValue={x:Null}}"
                                                  ItemsSource="{Binding}"
                                                  PlaceholderText="{x:Static lang:Resources.ExhibitPackageModal_VersionBoxPlaceholder}"
                                                  VerticalAlignment="Stretch"
                                                  VerticalContentAlignment="Center">
                                            <ComboBox.SelectionBoxItemTemplate>
                                                <DataTemplate DataType="m:ExhibitVersionModel">
                                                    <StackPanel>
                                                        <TextBlock
                                                            Text="{x:Static lang:Resources.ExhibitPackageModal_VersionLabelText}"
                                                            FontSize="{StaticResource SmallFontSize}"
                                                            Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                                        <TextBlock Text="{Binding VersionName}"
                                                                   TextTrimming="CharacterEllipsis" />
                                                    </StackPanel>
                                                </DataTemplate>
                                            </ComboBox.SelectionBoxItemTemplate>
                                            <ComboBox.ItemTemplate>
                                                <DataTemplate DataType="m:ExhibitVersionModel">
                                                    <StackPanel Spacing="4">
                                                        <TextBlock
                                                            Text="{Binding VersionName,FallbackValue=Display}" />
                                                        <Grid ColumnDefinitions="Auto,Auto,Auto,*"
                                                              ColumnSpacing="4">
                                                            <fi:SymbolIcon Grid.Column="0" Symbol="Flag"
                                                                           FontSize="{StaticResource SmallFontSize}"
                                                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                                            <TextBlock Grid.Column="1"
                                                                       Text="{Binding CompatibleLoaders}"
                                                                       FontSize="{StaticResource SmallFontSize}"
                                                                       TextTrimming="CharacterEllipsis"
                                                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                                            <fi:SymbolIcon Grid.Column="2" Symbol="Branch"
                                                                           FontSize="{StaticResource SmallFontSize}"
                                                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                                            <TextBlock Grid.Column="3"
                                                                       Text="{Binding CompatibleVersions}"
                                                                       FontSize="{StaticResource SmallFontSize}"
                                                                       TextTrimming="CharacterEllipsis"
                                                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                                        </Grid>
                                                        <DockPanel>
                                                            <StackPanel Orientation="Horizontal"
                                                                        DockPanel.Dock="Right">
                                                                <TextBlock Text="{Binding PublishedAt}" />
                                                            </StackPanel>
                                                            <husk:SwitchPresenter
                                                                TargetType="trident:ReleaseType"
                                                                Value="{Binding TypeRaw,FallbackValue=Release}">
                                                                <husk:SwitchCase Value="Release">
                                                                    <husk:Tag Classes="Success"
                                                                              CornerRadius="{StaticResource SmallCornerRadius}">
                                                                        <TextBlock
                                                                            Text="{x:Static lang:Resources.ReleaseType_Release}" />
                                                                    </husk:Tag>
                                                                </husk:SwitchCase>
                                                                <husk:SwitchCase Value="Beta">
                                                                    <husk:Tag Classes="Warning"
                                                                              CornerRadius="{StaticResource SmallCornerRadius}">
                                                                        <TextBlock
                                                                            Text="{x:Static lang:Resources.ReleaseType_Beta}" />
                                                                    </husk:Tag>
                                                                </husk:SwitchCase>
                                                                <husk:SwitchCase Value="Alpha">
                                                                    <husk:Tag Classes="Danger"
                                                                              CornerRadius="{StaticResource SmallCornerRadius}">
                                                                        <TextBlock
                                                                            Text="{x:Static lang:Resources.ReleaseType_Alpha}" />
                                                                    </husk:Tag>
                                                                </husk:SwitchCase>
                                                            </husk:SwitchPresenter>
                                                        </DockPanel>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </ComboBox.ItemTemplate>
                                        </ComboBox>
                                    </DataTemplate>
                                </husk:LazyContainer.SourceTemplate>
                            </husk:LazyContainer>
                        </TabStripItem>
                        <TabStripItem Grid.Column="1">
                            <StackPanel>
                                <fi:SymbolIcon Symbol="BoxCheckmark"
                                               FontSize="{StaticResource LargeFontSize}" />
                                <TextBlock Text="Auto" />
                            </StackPanel>
                        </TabStripItem>
                    </TabStrip>
                </Grid>
            </Border>
        </DataTemplate>
    </husk:Modal.Resources>
    <Grid ColumnDefinitions="*,Auto" ColumnSpacing="12">
        <Border Grid.Column="1" Width="300"
                Background="{StaticResource FlyoutBackgroundBrush}"
                BorderBrush="{StaticResource ControlBorderBrush}" BorderThickness="1"
                CornerRadius="{StaticResource MediumCornerRadius}">
            <Grid RowDefinitions="Auto,*">
                <Border Grid.Row="0" Background="{StaticResource ControlTranslucentFullBackgroundBrush}"
                        CornerRadius="{Binding Source={StaticResource MediumCornerRadius},Converter={x:Static husk:CornerRadiusConverters.Upper}}">
                    <Panel>
                        <Grid ColumnDefinitions="Auto,*,Auto" ColumnSpacing="12" Margin="10" VerticalAlignment="Top">
                            <ToggleButton Grid.Column="0" Classes="Small" BorderThickness="1"
                                          IsChecked="{Binding $parent[modals:ExhibitPackageModal].IsDetailPanelVisible,Mode=TwoWay}">
                                <fi:SymbolIcon Symbol="PanelLeftText" FontSize="{StaticResource MediumFontSize}" />
                            </ToggleButton>
                            <Button Grid.Column="2" Theme="{StaticResource OutlineButtonTheme}" Classes="Small"
                                    Background="Transparent" IsCancel="True"
                                    Command="{Binding $parent[modals:ExhibitPackageModal].DismissCommand}">
                                <fi:SymbolIcon Symbol="Dismiss" FontSize="{StaticResource MediumFontSize}" />
                            </Button>
                        </Grid>
                        <StackPanel Margin="18" Spacing="6">
                            <Border Height="72" Width="72" BorderBrush="{StaticResource OverlaySolidBackgroundBrush}"
                                    BorderThickness="4" CornerRadius="{StaticResource MediumCornerRadius}">
                                <Border.Background>
                                    <ImageBrush async:ImageBrushLoader.Source="{Binding Thumbnail}"
                                                Stretch="UniformToFill" />
                                </Border.Background>
                            </Border>
                            <TextBlock Text="{Binding ProjectName,FallbackValue=Project}"
                                       TextAlignment="Center" FontSize="{StaticResource LargeFontSize}"
                                       TextWrapping="Wrap" />
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Spacing="8">
                                <HyperlinkButton NavigateUri="{Binding Reference}">
                                    <ToolTip.Tip>
                                        <StackPanel Spacing="2">
                                            <Grid ColumnDefinitions="*,Auto" ColumnSpacing="4">
                                                <TextBlock Grid.Column="0"
                                                           Text="{x:Static lang:Resources.Shared_ExternalLinkLabelText}"
                                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                           FontSize="{StaticResource SmallFontSize}" />
                                                <fi:SymbolIcon Grid.Column="1" Symbol="Open"
                                                               Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                               FontSize="{StaticResource MediumFontSize}" />
                                            </Grid>
                                            <TextBlock
                                                Text="{Binding $parent[HyperlinkButton].NavigateUri}" />
                                        </StackPanel>
                                    </ToolTip.Tip>
                                    <StackPanel Orientation="Horizontal" Spacing="4">
                                        <TextBlock
                                            Text="{Binding Label,FallbackValue=Label,Converter={x:Static husk:StringConverters.ToUpper}}" />
                                        <fi:SymbolIcon Symbol="Open" FontSize="12" />
                                    </StackPanel>
                                </HyperlinkButton>
                                <TextBlock Text="{Binding AuthorName,StringFormat={}@{0},FallbackValue=@Author}" />
                            </StackPanel>
                        </StackPanel>
                    </Panel>
                </Border>
                <Grid Grid.Row="1" RowDefinitions="Auto,*,Auto" Margin="18" RowSpacing="12">
                    <TextBlock Grid.Row="0"
                               Text="{Binding Summary,FallbackValue=Summary}"
                               TextWrapping="Wrap" TextTrimming="CharacterEllipsis" MaxLines="4" />
                    <ItemsControl Grid.Row="1" ItemsSource="{Binding Tags}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate x:DataType="x:String">
                                <husk:Tag Content="{Binding}" FontSize="{StaticResource MediumFontSize}" />
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel ItemSpacing="4" LineSpacing="4" />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                    </ItemsControl>
                    <husk:SwitchPresenter Grid.Row="2"
                                          Value="{Binding $parent[modals:ExhibitPackageModal].Exhibit.State,Mode=OneWay,FallbackValue={x:Null}}"
                                          TargetType="m:ExhibitState">
                        <husk:SwitchCase Value="{x:Null}">
                            <Grid RowDefinitions="Auto,Auto" RowSpacing="12">
                                <ContentControl Grid.Row="0" ContentTemplate="{StaticResource VersionEditorTemplate}" />
                                <Grid Grid.Row="1" ColumnDefinitions="Auto,*" ColumnSpacing="12">
                                    <Button Grid.Column="0" Theme="{StaticResource OutlineButtonTheme}">
                                        <fi:SymbolIcon Symbol="Bookmark" FontSize="{StaticResource MediumFontSize}" />
                                    </Button>
                                    <Button Grid.Column="1" Classes="Primary"
                                            Command="{Binding $parent[modals:ExhibitPackageModal].ApplyCommand}">
                                        <StackPanel Orientation="Horizontal" Spacing="12">
                                            <fi:SymbolIcon Symbol="Add" FontSize="{StaticResource MediumFontSize}" />
                                            <TextBlock
                                                Text="{x:Static lang:Resources.ExhibitPackageModal_AddButtonText}" />
                                        </StackPanel>
                                    </Button>
                                </Grid>
                            </Grid>
                        </husk:SwitchCase>
                        <husk:SwitchCase Value="Editable">
                            <Grid RowDefinitions="Auto,4,Auto,12,Auto">
                                <husk:Tag Grid.Row="0" Classes="Success"
                                          ToolTip.Tip="{Binding $parent[modals:ExhibitPackageModal].Exhibit.InstalledVersionName}">
                                    <StackPanel Orientation="Horizontal" Spacing="4">
                                        <fi:SymbolIcon Symbol="MailInbox" FontSize="{StaticResource SmallFontSize}" />
                                        <husk:SwitchPresenter
                                            Value="{Binding $parent[modals:ExhibitPackageModal].Exhibit.InstalledVersionId,Mode=OneWay,Converter={x:Static ObjectConverters.IsNotNull},FallbackValue=False}"
                                            TargetType="x:Boolean">
                                            <husk:SwitchCase Value="True">
                                                <TextBlock TextTrimming="CharacterEllipsis">
                                                    <Run
                                                        Text="{x:Static lang:Resources.ExhibitPackageModal_InstalledVersionTagText}" />
                                                    <Run
                                                        Text="{Binding $parent[modals:ExhibitPackageModal].Exhibit.InstalledVersionName,FallbackValue=Version}" />
                                                </TextBlock>
                                            </husk:SwitchCase>
                                            <husk:SwitchCase Value="False">
                                                <TextBlock TextTrimming="CharacterEllipsis">
                                                    <Run
                                                        Text="{x:Static lang:Resources.ExhibitPackageModal_InstalledVersionTagText}" />
                                                    <Run FontWeight="{StaticResource ControlStrongFontWeight}"
                                                         Text="{x:Static lang:Resources.ExhibitPackageModal_UnspecifiedVersionTagText}" />
                                                </TextBlock>
                                            </husk:SwitchCase>
                                        </husk:SwitchPresenter>
                                    </StackPanel>
                                </husk:Tag>
                                <ContentControl Grid.Row="2" ContentTemplate="{StaticResource VersionEditorTemplate}" />
                                <Grid Grid.Row="4" ColumnDefinitions="Auto,*,Auto" ColumnSpacing="12">
                                    <Button Grid.Column="0" Theme="{StaticResource OutlineButtonTheme}">
                                        <fi:SymbolIcon Symbol="Bookmark" FontSize="{StaticResource MediumFontSize}" />
                                    </Button>
                                    <Button Grid.Column="1" Classes="Primary"
                                            Command="{Binding $parent[modals:ExhibitPackageModal].ApplyCommand}">
                                        <StackPanel Orientation="Horizontal" Spacing="12">
                                            <fi:SymbolIcon Symbol="Add" FontSize="{StaticResource MediumFontSize}" />
                                            <TextBlock
                                                Text="{x:Static lang:Resources.ExhibitPackageModal_ModifyButtonText}" />
                                        </StackPanel>
                                    </Button>
                                    <Button Grid.Column="2"
                                            Command="{Binding $parent[modals:ExhibitPackageModal].DeleteCommand}">
                                        <fi:SymbolIcon Symbol="Delete" FontSize="{StaticResource MediumFontSize}" />
                                    </Button>
                                </Grid>
                            </Grid>
                        </husk:SwitchCase>
                        <husk:SwitchCase Value="Locked">
                            <Border Background="{StaticResource ControlTranslucentFullBackgroundBrush}"
                                    CornerRadius="{StaticResource MediumCornerRadius}" Padding="12">
                                <Grid ColumnDefinitions="Auto,*" ColumnSpacing="12">
                                    <icons:PackIconLucide Grid.Column="0" Kind="Lock"
                                                          Height="{StaticResource ExtraLargeFontSize}"
                                                          VerticalAlignment="Center" />
                                    <StackPanel Grid.Column="1">
                                        <TextBlock
                                            Text="{x:Static lang:Resources.ExhibitPackageModal_LockedVersionLabelText}"
                                            FontSize="{StaticResource SmallFontSize}"
                                            Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                        <TextBlock
                                            Text="{Binding $parent[modals:ExhibitPackageModal].Exhibit.InstalledVersionName,FallbackValue=LockedVersionName}"
                                            TextTrimming="CharacterEllipsis"
                                            ToolTip.Tip="{Binding $parent[modals:ExhibitPackageModal].Exhibit.InstalledVersionName}" />
                                    </StackPanel>
                                </Grid>
                            </Border>
                        </husk:SwitchCase>
                        <husk:SwitchCase Value="Adding">
                            <Grid RowDefinitions="Auto,4,Auto,12,Auto">
                                <husk:Tag Grid.Row="0" Classes="Warning"
                                          ToolTip.Tip="{Binding $parent[modals:ExhibitPackageModal].SelectedVersion.VersionName,FallbackValue=Version}">
                                    <StackPanel Orientation="Horizontal" Spacing="4">
                                        <fi:SymbolIcon Symbol="AddCircle" FontSize="{StaticResource SmallFontSize}" />
                                        <husk:SwitchPresenter
                                            Value="{Binding $parent[modals:ExhibitPackageModal].Exhibit.PendingVersionId,Mode=OneWay,Converter={x:Static ObjectConverters.IsNotNull},FallbackValue=False}"
                                            TargetType="x:Boolean">
                                            <husk:SwitchCase Value="True">
                                                <TextBlock TextTrimming="CharacterEllipsis">
                                                    <Run
                                                        Text="{x:Static lang:Resources.ExhibitPackageModal_AddingVersionTagText}" />
                                                    <Run
                                                        Text="{Binding $parent[modals:ExhibitPackageModal].Exhibit.PendingVersionName,FallbackValue=Version}" />
                                                </TextBlock>
                                            </husk:SwitchCase>
                                            <husk:SwitchCase Value="False">
                                                <TextBlock TextTrimming="CharacterEllipsis">
                                                    <Run
                                                        Text="{x:Static lang:Resources.ExhibitPackageModal_AddingVersionTagText}" />
                                                    <Run FontWeight="{StaticResource ControlStrongFontWeight}"
                                                         Text="{x:Static lang:Resources.ExhibitPackageModal_UnspecifiedVersionTagText}" />
                                                </TextBlock>
                                            </husk:SwitchCase>
                                        </husk:SwitchPresenter>
                                    </StackPanel>
                                </husk:Tag>
                                <ContentControl Grid.Row="2" ContentTemplate="{StaticResource VersionEditorTemplate}" />
                                <Grid Grid.Row="4" ColumnDefinitions="Auto,*,Auto" ColumnSpacing="12">
                                    <Button Grid.Column="0" Theme="{StaticResource OutlineButtonTheme}">
                                        <fi:SymbolIcon Symbol="Bookmark" FontSize="{StaticResource MediumFontSize}" />
                                    </Button>
                                    <Button Grid.Column="1" Classes="Primary"
                                            Command="{Binding $parent[modals:ExhibitPackageModal].ApplyCommand}">
                                        <StackPanel Orientation="Horizontal" Spacing="12">
                                            <fi:SymbolIcon Symbol="Add" FontSize="{StaticResource MediumFontSize}" />
                                            <TextBlock
                                                Text="{x:Static lang:Resources.ExhibitPackageModal_ModifyButtonText}" />
                                        </StackPanel>
                                    </Button>
                                    <Button Grid.Column="2"
                                            Command="{Binding $parent[modals:ExhibitPackageModal].UndoCommand}">
                                        <fi:SymbolIcon Symbol="ArrowUndo" FontSize="{StaticResource MediumFontSize}" />
                                    </Button>
                                </Grid>
                            </Grid>
                        </husk:SwitchCase>
                        <husk:SwitchCase Value="Modifying">
                            <Grid RowDefinitions="Auto,4,Auto,4,Auto,12,Auto">
                                <husk:Tag Grid.Row="0" Classes="Success"
                                          ToolTip.Tip="{Binding $parent[modals:ExhibitPackageModal].Exhibit.InstalledVersionName}">
                                    <StackPanel Orientation="Horizontal" Spacing="4">
                                        <fi:SymbolIcon Symbol="MailInbox" FontSize="{StaticResource SmallFontSize}" />
                                        <husk:SwitchPresenter
                                            Value="{Binding $parent[modals:ExhibitPackageModal].Exhibit.InstalledVersionId,Mode=OneWay,Converter={x:Static ObjectConverters.IsNotNull},FallbackValue=False}"
                                            TargetType="x:Boolean">
                                            <husk:SwitchCase Value="True">
                                                <TextBlock TextTrimming="CharacterEllipsis">
                                                    <Run
                                                        Text="{x:Static lang:Resources.ExhibitPackageModal_InstalledVersionTagText}" />
                                                    <Run
                                                        Text="{Binding $parent[modals:ExhibitPackageModal].Exhibit.InstalledVersionName,FallbackValue=Version}" />
                                                </TextBlock>
                                            </husk:SwitchCase>
                                            <husk:SwitchCase Value="False">
                                                <TextBlock TextTrimming="CharacterEllipsis">
                                                    <Run
                                                        Text="{x:Static lang:Resources.ExhibitPackageModal_InstalledVersionTagText}" />
                                                    <Run FontWeight="{StaticResource ControlStrongFontWeight}"
                                                         Text="{x:Static lang:Resources.ExhibitPackageModal_UnspecifiedVersionTagText}" />
                                                </TextBlock>
                                            </husk:SwitchCase>
                                        </husk:SwitchPresenter>
                                    </StackPanel>
                                </husk:Tag>
                                <husk:Tag Grid.Row="2" Classes="Warning"
                                          ToolTip.Tip="{Binding $parent[modals:ExhibitPackageModal].SelectedVersion.VersionName,FallbackValue=Version}">
                                    <StackPanel Orientation="Horizontal" Spacing="4">
                                        <fi:SymbolIcon Symbol="Pen" FontSize="{StaticResource SmallFontSize}" />
                                        <husk:SwitchPresenter
                                            Value="{Binding $parent[modals:ExhibitPackageModal].Exhibit.PendingVersionId,Mode=OneWay,Converter={x:Static ObjectConverters.IsNotNull},FallbackValue=False}"
                                            TargetType="x:Boolean">
                                            <husk:SwitchCase Value="True">
                                                <TextBlock TextTrimming="CharacterEllipsis">
                                                    <Run
                                                        Text="{x:Static lang:Resources.ExhibitPackageModal_ModifyingTagText}" />
                                                    <Run
                                                        Text="{Binding $parent[modals:ExhibitPackageModal].Exhibit.PendingVersionName,FallbackValue=Version}" />
                                                </TextBlock>
                                            </husk:SwitchCase>
                                            <husk:SwitchCase Value="False">
                                                <TextBlock TextTrimming="CharacterEllipsis">
                                                    <Run
                                                        Text="{x:Static lang:Resources.ExhibitPackageModal_ModifyingTagText}" />
                                                    <Run FontWeight="{StaticResource ControlStrongFontWeight}"
                                                         Text="{x:Static lang:Resources.ExhibitPackageModal_UnspecifiedVersionTagText}" />
                                                </TextBlock>
                                            </husk:SwitchCase>
                                        </husk:SwitchPresenter>
                                    </StackPanel>
                                </husk:Tag>
                                <ContentControl Grid.Row="4" ContentTemplate="{StaticResource VersionEditorTemplate}" />
                                <Grid Grid.Row="6" ColumnDefinitions="Auto,*,Auto" ColumnSpacing="12">
                                    <Button Grid.Column="0" Theme="{StaticResource OutlineButtonTheme}">
                                        <fi:SymbolIcon Symbol="Bookmark" FontSize="{StaticResource MediumFontSize}" />
                                    </Button>
                                    <Button Grid.Column="1" Classes="Primary"
                                            Command="{Binding $parent[modals:ExhibitPackageModal].ApplyCommand}">
                                        <StackPanel Orientation="Horizontal" Spacing="12">
                                            <fi:SymbolIcon Symbol="Add" FontSize="{StaticResource MediumFontSize}" />
                                            <TextBlock
                                                Text="{x:Static lang:Resources.ExhibitPackageModal_ModifyButtonText}" />
                                        </StackPanel>
                                    </Button>
                                    <Button Grid.Column="2"
                                            Command="{Binding $parent[modals:ExhibitPackageModal].UndoCommand}">
                                        <fi:SymbolIcon Symbol="ArrowUndo" FontSize="{StaticResource MediumFontSize}" />
                                    </Button>
                                </Grid>
                            </Grid>
                        </husk:SwitchCase>
                        <husk:SwitchCase Value="Removing">
                            <Grid RowDefinitions="Auto,4,Auto,4,Auto,12,Auto">
                                <husk:Tag Grid.Row="0" Classes="Danger"
                                          ToolTip.Tip="{Binding $parent[modals:ExhibitPackageModal].Exhibit.InstalledVersionName}">
                                    <StackPanel Orientation="Horizontal" Spacing="4">
                                        <fi:SymbolIcon Symbol="Delete" FontSize="{StaticResource SmallFontSize}" />
                                        <TextBlock TextTrimming="CharacterEllipsis">
                                            <Run Text="{x:Static lang:Resources.ExhibitPackageModal_RemovingTagText}" />
                                            <Run
                                                Text="{Binding $parent[modals:ExhibitPackageModal].Exhibit.InstalledVersionName,FallbackValue=Version}" />
                                        </TextBlock>
                                    </StackPanel>
                                </husk:Tag>
                                <ContentControl Grid.Row="4" ContentTemplate="{StaticResource VersionEditorTemplate}" />
                                <Grid Grid.Row="6" ColumnDefinitions="Auto,*" ColumnSpacing="12">
                                    <Button Grid.Column="0" Theme="{StaticResource OutlineButtonTheme}">
                                        <fi:SymbolIcon Symbol="Bookmark" FontSize="{StaticResource MediumFontSize}" />
                                    </Button>
                                    <Button Grid.Column="1"
                                            Command="{Binding $parent[modals:ExhibitPackageModal].UndoCommand}">
                                        <StackPanel Orientation="Horizontal" Spacing="12">
                                            <fi:SymbolIcon Symbol="ArrowReset"
                                                           FontSize="{StaticResource MediumFontSize}" />
                                            <TextBlock
                                                Text="{x:Static lang:Resources.ExhibitPackageModal_RestoreButtonText}" />
                                        </StackPanel>
                                    </Button>
                                </Grid>
                            </Grid>
                        </husk:SwitchCase>
                    </husk:SwitchPresenter>
                </Grid>
            </Grid>
        </Border>
        <!-- 即使这个面板没有打开也会触发懒加载，因为 IsEffectivelyVisible 不是依赖属性也没法跟踪值 -->
        <husk:ConstrainedBox Grid.Column="0" AspectRatio="0.8"
                             IsVisible="{Binding $parent[modals:ExhibitPackageModal].IsDetailPanelVisible,Mode=OneWay}">
            <Border Background="{StaticResource FlyoutBackgroundBrush}"
                    BorderBrush="{StaticResource ControlBorderBrush}" BorderThickness="1"
                    CornerRadius="{StaticResource MediumCornerRadius}">
                <TabControl Theme="{StaticResource SolidTabControlTheme}" Padding="0">
                    <TabControl.ItemContainerTheme>
                        <ControlTheme TargetType="TabItem" BasedOn="{StaticResource SolidTabItemTheme}">
                            <Setter Property="Margin" Value="0" />
                            <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
                        </ControlTheme>
                    </TabControl.ItemContainerTheme>
                    <TabControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <StackPanel Orientation="Horizontal" Spacing="4" Margin="12" />
                        </ItemsPanelTemplate>
                    </TabControl.ItemsPanel>
                    <TabItem ToolTip.Tip="{x:Static lang:Resources.ExhibitPackageModal_AboutTabText}">
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Spacing="8" VerticalAlignment="Top">
                                <icons:PackIconLucide Kind="House" Height="{StaticResource MediumFontSize}"
                                                      Width="{StaticResource MediumFontSize}"
                                                      VerticalAlignment="Center" />
                                <!-- <TextBlock Text="About" VerticalAlignment="Center" /> -->
                            </StackPanel>
                        </TabItem.Header>
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <StackPanel>
                                <husk:ConstrainedBox AspectRatio="1.78"
                                                     IsVisible="{Binding Gallery.Count,Converter={x:Static husk:NumberConverters.IsNonZero}}">
                                    <Border Background="{StaticResource ControlTranslucentHalfBackgroundBrush}">
                                        <Panel>
                                            <Carousel Name="GalleryCarousel" ItemsSource="{Binding Gallery}">
                                                <Carousel.ItemTemplate>
                                                    <DataTemplate DataType="x:Uri">
                                                        <Image async:ImageLoader.Source="{Binding}"
                                                               RenderOptions.BitmapInterpolationMode="HighQuality" />
                                                    </DataTemplate>
                                                </Carousel.ItemTemplate>
                                            </Carousel>
                                            <husk:PipsPager VerticalAlignment="Bottom" HorizontalAlignment="Center"
                                                            Margin="6"
                                                            SelectedIndex="{Binding #GalleryCarousel.SelectedIndex,Mode=TwoWay}"
                                                            ItemCount="{Binding #GalleryCarousel.ItemCount}" />
                                        </Panel>
                                    </Border>
                                </husk:ConstrainedBox>
                                <husk:LazyContainer
                                    Source="{Binding $parent[modals:ExhibitPackageModal].LazyDescription}">
                                    <husk:LazyContainer.SourceTemplate>
                                        <DataTemplate DataType="x:String">
                                            <mdxaml:MarkdownScrollViewer Markdown="{Binding}" Margin="12">
                                                <mdxaml:MarkdownScrollViewer.Engine>
                                                    <md:Markdown
                                                        HyperlinkCommand="{Binding $parent[modals:ExhibitPackageModal].NavigateUriCommand}" />
                                                </mdxaml:MarkdownScrollViewer.Engine>
                                            </mdxaml:MarkdownScrollViewer>
                                        </DataTemplate>
                                    </husk:LazyContainer.SourceTemplate>
                                </husk:LazyContainer>
                            </StackPanel>
                        </ScrollViewer>
                    </TabItem>
                    <TabItem ToolTip.Tip="{x:Static lang:Resources.ExhibitPackageModal_DependenciesTabText}">
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Spacing="8" VerticalAlignment="Top">
                                <icons:PackIconLucide Kind="List" Height="{StaticResource MediumFontSize}"
                                                      Width="{StaticResource MediumFontSize}"
                                                      VerticalAlignment="Center" />
                                <TextBlock Text="{x:Static lang:Resources.ExhibitPackageModal_DependenciesTabText}"
                                           VerticalAlignment="Center" />
                                <husk:Tag Classes="Status">
                                    <TextBlock
                                        Text="{Binding $parent[modals:ExhibitPackageModal].LazyDependencies.((m:ExhibitDependencyCollection)Value).Count,Mode=OneWay,FallbackValue=N/A}" />
                                </husk:Tag>
                            </StackPanel>
                        </TabItem.Header>
                        <Grid RowDefinitions="Auto,*">
                            <Border Grid.Row="0" Margin="12,0" CornerRadius="{StaticResource MediumCornerRadius}"
                                    Padding="12" Background="{StaticResource ControlTranslucentFullBackgroundBrush}">
                                <StackPanel>
                                    <TextBlock
                                        Text="{x:Static lang:Resources.ExhibitPackageModal_DependenciesTabPrompt}" />
                                    <TextBlock FontWeight="{StaticResource ControlStrongFontWeight}"
                                               Text="{Binding $parent[modals:ExhibitPackageModal].LazyDependencies.((m:ExhibitDependencyCollection)Value).VersionName,FallbackValue=None}" />
                                </StackPanel>
                            </Border>
                            <Panel Grid.Row="1">
                                <StackPanel VerticalAlignment="Center"
                                            IsVisible="{Binding $parent[modals:ExhibitPackageModal].LazyDependencies.((m:ExhibitDependencyCollection)Value).Count,Converter={x:Static husk:NumberConverters.IsZero},FallbackValue=True}"
                                            Spacing="8">
                                    <icons:PackIconLucide Kind="Box" Height="{StaticResource ExtraLargeFontSize}"
                                                          Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                          Width="{StaticResource ExtraLargeFontSize}"
                                                          HorizontalAlignment="Center" />
                                    <TextBlock Text="{x:Static lang:Resources.Shared_EmptyListLabelText}"
                                               FontSize="{StaticResource LargeFontSize}"
                                               Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                               HorizontalAlignment="Center" />
                                </StackPanel>
                                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="12">
                                    <husk:LazyContainer MinHeight="72"
                                                        Source="{Binding $parent[modals:ExhibitPackageModal].LazyDependencies,Mode=OneWay}">
                                        <husk:LazyContainer.SourceTemplate>
                                            <DataTemplate x:DataType="m:ExhibitDependencyCollection">
                                                <ItemsControl
                                                    ItemsSource="{Binding Mode=OneWay}">
                                                    <ItemsControl.ItemTemplate>
                                                        <DataTemplate x:DataType="m:ExhibitDependencyModel">
                                                            <controls:ExhibitDependencyButton
                                                                Command="{Binding $parent[modals:ExhibitPackageModal].ViewPackageCommand,Mode=OneWay}"
                                                                CommandParameter="{Binding Exhibit}">
                                                                <controls:ExhibitDependencyButton.IsChecked>
                                                                    <MultiBinding
                                                                        Converter="{x:Static BoolConverters.Or}">
                                                                        <Binding Path="Exhibit.State"
                                                                            Converter="{x:Static husk:ObjectConverters.Match}"
                                                                            ConverterParameter="Editable" />
                                                                        <Binding Path="Exhibit.State"
                                                                            Converter="{x:Static husk:ObjectConverters.Match}"
                                                                            ConverterParameter="Locked" />
                                                                        <Binding Path="Exhibit.State"
                                                                            Converter="{x:Static husk:ObjectConverters.Match}"
                                                                            ConverterParameter="Adding" />
                                                                        <Binding Path="Exhibit.State"
                                                                            Converter="{x:Static husk:ObjectConverters.Match}"
                                                                            ConverterParameter="Modifying" />
                                                                    </MultiBinding>
                                                                </controls:ExhibitDependencyButton.IsChecked>
                                                            </controls:ExhibitDependencyButton>
                                                        </DataTemplate>
                                                    </ItemsControl.ItemTemplate>
                                                </ItemsControl>
                                            </DataTemplate>
                                        </husk:LazyContainer.SourceTemplate>
                                    </husk:LazyContainer>
                                </ScrollViewer>
                            </Panel>
                        </Grid>
                    </TabItem>
                    <TabItem ToolTip.Tip="{x:Static lang:Resources.ExhibitPackageModal_ChangelogsTabText}">
                        <TabItem.Header>
                            <StackPanel Orientation="Horizontal" Spacing="8" VerticalAlignment="Top">
                                <icons:PackIconLucide Kind="FileText" Height="{StaticResource MediumFontSize}"
                                                      Width="{StaticResource MediumFontSize}"
                                                      VerticalAlignment="Center" />
                                <TextBlock Text="{x:Static lang:Resources.ExhibitPackageModal_ChangelogsTabText}"
                                           VerticalAlignment="Center" />
                            </StackPanel>
                        </TabItem.Header>
                        <Panel>
                            <ScrollViewer VerticalScrollBarVisibility="Auto"
                                          IsVisible="{Binding $parent[modals:ExhibitPackageModal].SelectedVersion,Converter={x:Static ObjectConverters.IsNotNull},FallbackValue=False}">
                                <Grid RowDefinitions="Auto,*">
                                    <Border Grid.Row="0" Margin="12,0"
                                            CornerRadius="{StaticResource MediumCornerRadius}"
                                            Padding="12"
                                            Background="{StaticResource ControlTranslucentFullBackgroundBrush}">
                                        <StackPanel>
                                            <TextBlock
                                                Text="{x:Static lang:Resources.ExhibitPackageModal_ChangelogsTabPrompt}" />
                                            <TextBlock FontWeight="{StaticResource ControlStrongFontWeight}"
                                                       Text="{Binding $parent[modals:ExhibitPackageModal].SelectedVersion.VersionName,FallbackValue=None}" />
                                        </StackPanel>
                                    </Border>
                                    <husk:LazyContainer Grid.Row="1"
                                                        Source="{Binding $parent[modals:ExhibitPackageModal].LazyChangelog}">
                                        <husk:LazyContainer.SourceTemplate>
                                            <DataTemplate DataType="x:String">
                                                <mdxaml:MarkdownScrollViewer Markdown="{Binding}" Margin="12" />
                                            </DataTemplate>
                                        </husk:LazyContainer.SourceTemplate>
                                    </husk:LazyContainer>
                                </Grid>
                            </ScrollViewer>
                            <StackPanel VerticalAlignment="Center"
                                        IsVisible="{Binding $parent[modals:ExhibitPackageModal].SelectedVersion,Converter={x:Static ObjectConverters.IsNull},FallbackValue=False}"
                                        Spacing="8">
                                <icons:PackIconLucide Kind="Text" Height="{StaticResource ExtraLargeFontSize}"
                                                      Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                      Width="{StaticResource ExtraLargeFontSize}"
                                                      HorizontalAlignment="Center" />
                                <TextBlock Text="{x:Static lang:Resources.ExhibitPackageModal_EmptyListLabelText}"
                                           FontSize="{StaticResource LargeFontSize}"
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                           HorizontalAlignment="Center" />
                            </StackPanel>
                        </Panel>
                    </TabItem>
                </TabControl>
            </Border>
        </husk:ConstrainedBox>
    </Grid>
</husk:Modal>