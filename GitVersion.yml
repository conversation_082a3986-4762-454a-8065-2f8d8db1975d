mode: ContinuousDeployment
tag-prefix: '[vV]'
major-version-bump-message: '\+semver:\s?(breaking|major)'
minor-version-bump-message: '\+semver:\s?(feature|minor)'
patch-version-bump-message: '\+semver:\s?(fix|patch)'
no-bump-message: '\+semver:\s?(none|skip)'
commit-message-incrementing: Enabled
update-build-number: true
assembly-versioning-scheme: MajorMinorPatchTag
assembly-file-versioning-scheme: MajorMinorPatchTag
assembly-informational-format: '{InformationalVersion}'
branches:
    main:
        regex: ^master$|^main$
        label: ''
        increment: Inherit      # 根据来源决定版本增量
        prevent-increment-of-merged-branch-version: false
        track-merge-target: true
        source-branches: ['release', 'hotfix']  # 只接受 release 和 hotfix
        is-release-branch: true
        is-mainline: true
    develop:
        regex: ^dev(elop)?(ment)?$
        label: alpha
        increment: Minor        # develop 用于功能开发，增加 minor 版本
        prevent-increment-of-merged-branch-version: false
        track-merge-target: false
        source-branches: ['feature', 'hotfix']  # 接受功能分支和修复合并
        tracks-release-branches: true
        is-release-branch: false
        is-mainline: false
    release:
        regex: ^releases?[/-]v?(?<version>\d+\.\d+)$
        label: rc
        increment: None         # 版本号固定，只增加 rc 构建号
        prevent-increment-of-merged-branch-version: true
        track-merge-target: false
        source-branches: ['develop']
        tracks-release-branches: false
        is-release-branch: true
        is-mainline: false
    hotfix:
        regex: ^hotfix(es)?[/-]v?(?<version>\d+\.\d+\.\d+)$
        label: beta
        increment: Patch        # hotfix 增加 patch 版本
        prevent-increment-of-merged-branch-version: false
        track-merge-target: false
        source-branches: ['main']  # 从 main 分支创建
        tracks-release-branches: false
        is-release-branch: false
        is-mainline: false
    feature:
        regex: ^features?[/-]
        label: '{BranchName}'
        increment: Inherit
        prevent-increment-of-merged-branch-version: false
        track-merge-target: false
        source-branches: ['develop']
        tracks-release-branches: false
        is-release-branch: false
        is-mainline: false
