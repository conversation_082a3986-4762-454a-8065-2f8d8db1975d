mode: ContinuousDeployment
tag-prefix: '[vV]'
continuous-delivery-fallback-tag: ci
major-version-bump-message: '\+semver:\s?(breaking|major)'
minor-version-bump-message: '\+semver:\s?(feature|minor)'
patch-version-bump-message: '\+semver:\s?(fix|patch)'
no-bump-message: '\+semver:\s?(none|skip)'
legacy-semver-padding: 4
build-metadata-padding: 4
commits-since-version-source-padding: 4
tag-pre-release-weight: 60000
commit-message-incrementing: Enabled
merge-message-formats: {}
update-build-number: true
assembly-versioning-scheme: MajorMinorPatchTag
assembly-file-versioning-scheme: MajorMinorPatchTag
assembly-informational-format: '{InformationalVersion}'
versioning-mode: ContinuousDeployment
increment: Inherit
prevent-increment-of-merged-branch-version: false
track-merge-target: false
track-merge-message: true
commit-date-format: 'yyyy-MM-dd'
ignore:
  sha: []
branches:
  main:
    regex: ^master$|^main$
    mode: ContinuousDeployment
    tag: ''
    increment: Patch
    prevent-increment-of-merged-branch-version: false
    track-merge-target: false
    track-merge-message: true
    source-branches: ['develop', 'release']
    tracks-release-branches: false
    is-release-branch: true
    is-mainline: true
    pre-release-weight: 55000
  develop:
    regex: ^dev(elop)?(ment)?$
    mode: ContinuousDeployment
    tag: alpha
    increment: Minor
    prevent-increment-of-merged-branch-version: false
    track-merge-target: true
    track-merge-message: true
    source-branches: []
    tracks-release-branches: true
    is-release-branch: false
    is-mainline: false
    pre-release-weight: 0
  release:
    regex: ^releases?[/-]
    mode: ContinuousDeployment
    tag: rc
    increment: None
    prevent-increment-of-merged-branch-version: true
    track-merge-target: false
    track-merge-message: true
    source-branches: ['develop', 'main', 'support', 'release']
    tracks-release-branches: false
    is-release-branch: true
    is-mainline: false
    pre-release-weight: 30000
  feature:
    regex: ^features?[/-]
    mode: ContinuousDeployment
    tag: useBranchName
    increment: Inherit
    prevent-increment-of-merged-branch-version: false
    track-merge-target: false
    track-merge-message: true
    source-branches: ['develop', 'main', 'release', 'feature', 'support', 'hotfix']
    tracks-release-branches: false
    is-release-branch: false
    is-mainline: false
    pre-release-weight: 30000
  pull-request:
    regex: ^(pull|pull\-requests|pr)[/-]
    mode: ContinuousDeployment
    tag: PullRequest
    increment: Inherit
    prevent-increment-of-merged-branch-version: false
    track-merge-target: false
    track-merge-message: true
    source-branches: ['develop', 'main', 'release', 'feature', 'support', 'hotfix']
    tracks-release-branches: false
    is-release-branch: false
    is-mainline: false
    pre-release-weight: 30000
  hotfix:
    regex: ^hotfix(es)?[/-]
    mode: ContinuousDeployment
    tag: beta
    increment: Patch
    prevent-increment-of-merged-branch-version: false
    track-merge-target: false
    track-merge-message: true
    source-branches: ['release', 'main', 'support', 'hotfix']
    tracks-release-branches: false
    is-release-branch: false
    is-mainline: false
    pre-release-weight: 30000
  support:
    regex: ^support[/-]
    mode: ContinuousDeployment
    tag: ''
    increment: Patch
    prevent-increment-of-merged-branch-version: true
    track-merge-target: false
    track-merge-message: true
    source-branches: ['main']
    tracks-release-branches: false
    is-release-branch: false
    is-mainline: true
    pre-release-weight: 55000
