mode: ContinuousDeployment
tag-prefix: '[vV]'
major-version-bump-message: '\+semver:\s?(breaking|major)'
minor-version-bump-message: '\+semver:\s?(feature|minor)'
patch-version-bump-message: '\+semver:\s?(fix|patch)'
no-bump-message: '\+semver:\s?(none|skip)'
commit-message-incrementing: Enabled
update-build-number: true
assembly-versioning-scheme: MajorMinorPatchTag
assembly-file-versioning-scheme: MajorMinorPatchTag
assembly-informational-format: '{InformationalVersion}'
branches:
    main:
        regex: ^master$|^main$
        label: ''
        increment: Inherit  # 根据提交消息或合并内容决定
    develop:
        regex: ^dev(elop)?(ment)?$
        label: alpha
        increment: Inherit  # 根据提交消息决定，不自动增加
    release:
        regex: ^releases?[/-]
        label: rc
        increment: None
    feature:
        regex: ^features?[/-]
        label: useBranchName
        increment: Inherit
