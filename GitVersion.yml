mode: ContinuousDeployment
tag-prefix: '[vV]'
major-version-bump-message: '\+semver:\s?(breaking|major)'
minor-version-bump-message: '\+semver:\s?(feature|minor)'
patch-version-bump-message: '\+semver:\s?(fix|patch)'
no-bump-message: '\+semver:\s?(none|skip)'
commit-message-incrementing: Enabled
update-build-number: true
assembly-versioning-scheme: MajorMinorPatchTag
assembly-file-versioning-scheme: MajorMinorPatchTag
assembly-informational-format: '{InformationalVersion}'
branches:
    main:
        regex: ^master$|^main$
        label: ''
        increment: Inherit      # 根据来源决定：release→正式版，develop→patch版
        prevent-increment-of-merged-branch-version: false
        track-merge-target: true
        source-branches: ['develop', 'release']
        is-release-branch: true
        is-mainline: true
    develop:
        regex: ^dev(elop)?(ment)?$
        label: ''               # develop 没有版本标签
        increment: None         # develop 不产生版本号
        prevent-increment-of-merged-branch-version: false
        track-merge-target: false
        source-branches: ['feature']
        tracks-release-branches: false
        is-release-branch: false
        is-mainline: false
    release:
        regex: ^releases?[/-]v?(?<version>\d+\.\d+)$
        label: rc
        increment: None         # 版本号固定，只增加 rc 构建号
        prevent-increment-of-merged-branch-version: true
        track-merge-target: false
        source-branches: ['develop']
        tracks-release-branches: false
        is-release-branch: true
        is-mainline: false
    feature:
        regex: ^features?[/-]
        label: '{BranchName}'
        increment: Inherit
        prevent-increment-of-merged-branch-version: false
        track-merge-target: false
        source-branches: ['develop']
        tracks-release-branches: false
        is-release-branch: false
        is-mainline: false
