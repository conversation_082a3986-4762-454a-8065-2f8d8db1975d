﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:Polymerium.App.Controls"
                    xmlns:async="clr-namespace:AsyncImageLoader;assembly=AsyncImageLoader.Avalonia"
                    xmlns:m="clr-namespace:Polymerium.App.Models"
                    xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
                    xmlns:components="clr-namespace:Polymerium.App.Components"
                    xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia">
    <ControlTheme x:Key="{x:Type controls:ExhibitPackageButton}" TargetType="controls:ExhibitPackageButton">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Background" Value="{StaticResource CardBackgroundBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource MediumCornerRadius}" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlBorderBrush}" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Template">
            <ControlTemplate x:DataType="m:ExhibitModel">
                <Border Name="Background" CornerRadius="{TemplateBinding CornerRadius}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                    <Border.Transitions>
                        <Transitions>
                            <BrushTransition Property="BorderBrush" Easing="SineEaseOut"
                                             Duration="{StaticResource ControlFasterAnimationDuration}" />
                            <TransformOperationsTransition Property="RenderTransform" Easing="SineEaseOut"
                                                           Duration="{StaticResource ControlFastestAnimationDuration}" />
                        </Transitions>
                    </Border.Transitions>
                    <ToolTip.Tip>
                        <Grid RowDefinitions="*,Auto" MaxWidth="300" Margin="{StaticResource ToolTipFillMargin}">
                            <Border Grid.Row="0">
                                <StackPanel Margin="12">
                                    <TextBlock Text="{Binding ProjectName}" FontSize="{StaticResource LargeFontSize}"
                                               FontWeight="{StaticResource ControlStrongFontWeight}"
                                               TextWrapping="Wrap" />
                                    <TextBlock Text="{Binding Summary}" TextWrapping="Wrap" />
                                </StackPanel>
                            </Border>
                            <Border Grid.Row="1" Background="{StaticResource ControlBackgroundBrush}"
                                    CornerRadius="{Binding Source={StaticResource SmallCornerRadius},Converter={x:Static husk:CornerRadiusConverters.Lower}}"
                                    Padding="6">
                                <Grid ColumnDefinitions="*,Auto" ColumnSpacing="4">
                                    <Border Grid.Column="0" BorderBrush="{StaticResource ControlBorderBrush}"
                                            BorderThickness="1" HorizontalAlignment="Left"
                                            Padding="4,2"
                                            CornerRadius="{StaticResource SmallCornerRadius}"
                                            Background="{StaticResource LayerBackgroundBrush}">
                                        <StackPanel Orientation="Horizontal" Spacing="4">
                                            <icons:PackIconLucide Kind="CircleUser"
                                                                  Height="{StaticResource MediumFontSize}"
                                                                  Width="{StaticResource MediumFontSize}"
                                                                  VerticalAlignment="Center" />
                                            <TextBlock Text="{Binding Author}" VerticalAlignment="Center" />
                                        </StackPanel>
                                    </Border>
                                    <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="4">
                                        <Border BorderBrush="{StaticResource ControlBorderBrush}" BorderThickness="1"
                                                Padding="4,2" CornerRadius="{StaticResource SmallCornerRadius}"
                                                Background="{StaticResource LayerBackgroundBrush}">
                                            <StackPanel Orientation="Horizontal" Spacing="4">
                                                <icons:PackIconLucide Kind="CircleArrowDown"
                                                                      Height="{StaticResource MediumFontSize}"
                                                                      Width="{StaticResource MediumFontSize}"
                                                                      VerticalAlignment="Center" />
                                                <TextBlock Text="{Binding Downloads}" VerticalAlignment="Center" />
                                            </StackPanel>
                                        </Border>
                                        <Border BorderBrush="{StaticResource ControlBorderBrush}" BorderThickness="1"
                                                Padding="4,2" CornerRadius="{StaticResource SmallCornerRadius}"
                                                Background="{StaticResource LayerBackgroundBrush}">
                                            <StackPanel Orientation="Horizontal" Spacing="4">
                                                <icons:PackIconLucide Kind="Clock"
                                                                      Height="{StaticResource MediumFontSize}"
                                                                      Width="{StaticResource MediumFontSize}"
                                                                      VerticalAlignment="Center" />
                                                <TextBlock Text="{Binding UpdatedAt}" VerticalAlignment="Center" />
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>
                                </Grid>
                            </Border>
                        </Grid>
                    </ToolTip.Tip>
                    <Grid RowDefinitions="Auto,*">
                        <Border Grid.Row="0">
                            <Panel>
                                <Border Height="64" Width="64" Margin="12"
                                        CornerRadius="{StaticResource SmallCornerRadius}" HorizontalAlignment="Center"
                                        VerticalAlignment="Center">
                                    <Border.Background>
                                        <ImageBrush async:ImageBrushLoader.Source="{Binding Thumbnail}" />
                                    </Border.Background>
                                </Border>
                                <components:ExhibitStatePresenter RenderTransform="translateY(8px)"
                                                                  HorizontalAlignment="Center"
                                                                  VerticalAlignment="Bottom" State="{Binding State}" />
                            </Panel>
                        </Border>
                        <Border Grid.Row="1" Padding="8"
                                Background="{StaticResource ControlTranslucentHalfBackgroundBrush}"
                                CornerRadius="{Binding Source={StaticResource MediumCornerRadius},Converter={x:Static husk:CornerRadiusConverters.Lower}}">
                            <Grid>
                                <TextBlock Text="{Binding ProjectName}"
                                           FontWeight="{StaticResource ControlStrongFontWeight}" TextAlignment="Center"
                                           TextTrimming="CharacterEllipsis"
                                           TextWrapping="Wrap" MaxLines="2" />
                            </Grid>
                        </Border>
                    </Grid>
                </Border>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover /template/ Border#Background">
            <Setter Property="BorderBrush"
                    Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
        </Style>
        <Style Selector="^:pointerover /template/ Border#Background">
            <Setter Property="RenderTransform" Value="scale(1.05)" />
        </Style>
        <Style Selector="^:pressed /template/ Border#Background">
            <Setter Property="RenderTransform" Value="scale(0.98)" />
        </Style>

        <Style Selector="^:disabled /template/ Border#Background">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>