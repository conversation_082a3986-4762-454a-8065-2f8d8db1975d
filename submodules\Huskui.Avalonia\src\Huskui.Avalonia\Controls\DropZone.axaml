﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia"
                    xmlns:fi="using:FluentIcons.Avalonia">
    <Design.PreviewWith>
        <Panel Background="{StaticResource SystemChromeWhiteColor}">
            <local:DropZone Margin="12">
                <Panel>
                    <Rectangle RadiusX="6" RadiusY="6"
                               StrokeThickness="2"
                               Stroke="{StaticResource ControlBorderBrush}"
                               StrokeDashArray="6,2"
                               StrokeLineCap="Round"
                               StrokeJoin="Round" />
                    <StackPanel Margin="16,12">
                        <StackPanel Orientation="Horizontal" Spacing="4">
                            <fi:SymbolIcon Symbol="Attach" FontSize="14" IconVariant="Filled" />
                            <TextBlock FontWeight="{StaticResource ControlStrongFontWeight}" Text="Drag and drop, or" />
                            <HyperlinkButton Content="Browser files..."
                                             FontWeight="{StaticResource ControlStrongFontWeight}"
                                             Foreground="{StaticResource ControlAccentForegroundBrush}" />
                        </StackPanel>
                        <TextBlock Text="Up to 12MB" HorizontalAlignment="Center"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </StackPanel>
                </Panel>
            </local:DropZone>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type local:DropZone}" TargetType="local:DropZone">
        <Setter Property="Padding" Value="12" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="{TemplateBinding Background}"
                        BackgroundSizing="{TemplateBinding BackgroundSizing}"
                        CornerRadius="{TemplateBinding CornerRadius}" BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}">
                    <ContentPresenter Content="{TemplateBinding Content}"
                                      Padding="{TemplateBinding Padding}"
                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" />
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>