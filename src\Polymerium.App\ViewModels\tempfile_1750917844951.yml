﻿name: Release Application

on:
  push:
    tags:
      - 'v*' # 触发条件：tag 名以 "v" 开头，例如 v1.0.0

jobs:
  build-and-publish:
    name: Build and Publish with Velopack
    runs-on: windows-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup .NET SDK
        uses: actions/setup-dotnet@v3
        with:
          version: '7.x' # 根据项目需要选择合适的 .NET 版本

      - name: Install Velopack CLI
        run: dotnet tool install -g --add-source https://api.nuget.org/v3/index.json velopack

      - name: Restore, Build, and Pack Application
        run: |
          dotnet restore
          dotnet build --configuration Release
          dotnet pack --configuration Release

      - name: Publish to GitHub Releases using Velopack
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          # 假设你的项目文件是 MyApplication.csproj
          velopack publish github --token $GITHUB_TOKEN --project MyApplication.csproj --version ${{ github.ref_name }}
