﻿# Trident V2

## 目录结构

`*.toml` 类型的文件为可用户编辑的配置文件，用户读写友好且可直接向互联网分享文件内容。
`data.*.json` 类型的文件为程序记录信息，该文件缺失或构建后删除并不致命；不建议用户处于纠错目的外的修改。
`build` 构建产出目录，游戏运行时使用该目录作为运行目录；内部的文件包含来自其他地方的软链接和实体文件。
`persist` 持久化目录，

### `profile.toml` 元数据信息

该文件储存一个能被用户编辑并作为整合包元数据导出的最小实例信息。
例如：实例的附加信息（实例名），能够保证实例构建幂等的最小数据（构建层），运行时需要覆盖的设定。

其他的诸如运行统计数据、构建偏好等存放于其他配置文件中。

### `preferences.toml` 实例偏好

所有不宜放入 `profile.toml` 的设置项都会放入这里。

### `data.lock.json` 构建锁

扁平化的构建最终描述文件，只包含和游戏运行相关的**最终**信息。
该文件不具有移植能力，但可以仅通过该文件还原出一个一模一样的构建。
*不具有移植能力是由于 GameArguments 中要求传递绝对路径，导致失去可移植性*

元数据中的模糊包版本会在生成锁文件时确定为一个确定的包版本（实际只有文件信息没有包版本信息）。
只要元数据没修改，构建锁就不会更新。

## Trident 构建机制

`build` 目录内包含软链接和实体文件，实体文件不受托管。
软链接的文件来自 `cache/objects`，例如模组资源包等。

### 持久化

部分目录会使用 `persist` 的持久化来避免每次重置时文件丢失，例如 `screenshots` 目录和 `options.txt` 文件。
持久化的文件和目录会在构建时被软链接到 `build`。

### 快照

快照文件会按日期时间存储在 `snapshots` 目录中，`snapshots/{datetime}/data.shot.json` 会储存关于快照的元数据。

元数据包含快照的附属信息：快照备注，和构造的信息：`profile.toml` 中的 `setup`，需要还原的文件和目录。

快照通常只会拍摄 `config`、`defaultconfigs` 和 `options.txt` 等配置文件信息、构建层和游戏存档 `saves`。
除此之外的文件和目录不具有拍摄快照的意义。

在还原的时候，构建层会直接替换掉当前实例的元数据中的构建层并应用到 `profile.toml` 中。
文件会替换 `build` 目录中对应文件，目录会删除目录后再复制过去。

拍摄或还原时检测到文件是软链接则是操作软链接的目标文件，例如持久化文件和目录，哪怕是 `objects` 内的模组文件也会执行操作。

### 构造的源信息

在整合包导入的时候直接把文件的 `Overrides` 目录内容解压到 `import` 目录内，固化时查漏补缺。

### 版本锁

构造中的包列表不需要包含全部的依赖信息，只有在需要解决依赖版本冲突时才需要将依赖的特定版本加入构造的包列表。
缺失的包会在构建时绘制依赖图谱，并收集缺失部分的依赖，从源获取。所有锁定版本后的包信息会写入版本锁，用于之后的构建。