﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <ControlTheme x:Key="{x:Type local:ConstrainedBox}"
                  TargetType="local:ConstrainedBox">
        <Setter Property="Template">
            <ControlTemplate>
                <ContentPresenter Content="{TemplateBinding Content}"
                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>