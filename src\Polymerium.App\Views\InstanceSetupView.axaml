﻿<controls:Subpage xmlns="https://github.com/avaloniaui"
                  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                  xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                  xmlns:vm="using:Polymerium.App.ViewModels"
                  xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
                  xmlns:cp="using:Polymerium.App.Components"
                  xmlns:m="using:Polymerium.App.Models"
                  xmlns:controls="using:Polymerium.App.Controls"
                  xmlns:v="clr-namespace:Polymerium.App.Views"
                  xmlns:async="clr-namespace:AsyncImageLoader;assembly=AsyncImageLoader.Avalonia"
                  xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
                  xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
                  mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="600" Padding="0"
                  x:Class="Polymerium.App.Views.InstanceSetupView" x:DataType="vm:InstanceSetupViewModel"
                  ScrollViewer.VerticalScrollBarVisibility="Disabled">
    <husk:BusyContainer>
        <husk:BusyContainer.IsBusy>
            <MultiBinding Converter="{x:Static BoolConverters.Or}">
                <Binding Path="State" Converter="{x:Static husk:ObjectConverters.Match}" ConverterParameter="Updating" />
                <Binding Path="State" Converter="{x:Static husk:ObjectConverters.Match}"
                         ConverterParameter="Deploying" />
            </MultiBinding>
        </husk:BusyContainer.IsBusy>
        <husk:BusyContainer.PendingContent>
            <husk:Card HorizontalAlignment="Center" VerticalAlignment="Center"
                       Background="{StaticResource OverlaySolidBackgroundBrush}">
                <StackPanel Spacing="8" Margin="24">
                    <husk:ProgressRing HorizontalAlignment="Center" Height="56" Width="56"
                                       Value="{Binding UpdatingProgress,Mode=OneWay}">
                        <husk:ProgressRing.IsIndeterminate>
                            <MultiBinding Converter="{x:Static BoolConverters.Or}">
                                <Binding Path="UpdatingPending" />
                                <Binding Path="State" Converter="{x:Static husk:ObjectConverters.Match}"
                                         ConverterParameter="Deploying" />
                            </MultiBinding>
                        </husk:ProgressRing.IsIndeterminate>
                        <husk:ProgressRing.Transitions>
                            <Transitions>
                                <DoubleTransition Property="Value" Easing="SineEaseOut"
                                                  Duration="{StaticResource ControlNormalAnimationDuration}" />
                            </Transitions>
                        </husk:ProgressRing.Transitions>
                    </husk:ProgressRing>
                    <TextBlock Text="{x:Static lang:Resources.InstanceSetupView_InoperableLabelText}"
                               FontWeight="{StaticResource ControlStrongFontWeight}"
                               HorizontalAlignment="Center" />
                </StackPanel>
            </husk:Card>
        </husk:BusyContainer.PendingContent>
        <Grid RowDefinitions="Auto,0,*">
            <Grid Grid.Row="0" ColumnDefinitions="*,Auto" ColumnSpacing="12"
                  Margin="{StaticResource PageHeaderlessContentMargin}">
                <Grid Grid.Column="0" RowDefinitions="*,4,Auto">
                    <TextBlock Grid.Row="0" Text="{Binding Basic.Name,FallbackValue=Name}"
                               FontSize="{StaticResource ExtraLargeFontSize}" TextWrapping="Wrap" MaxLines="2"
                               TextTrimming="CharacterEllipsis" />
                    <StackPanel Grid.Row="2" Orientation="Horizontal" Spacing="12">
                        <Border BorderThickness="3,0,0,0" CornerRadius="{StaticResource SmallCornerRadius}"
                                BorderBrush="{StaticResource ControlAccentBackgroundBrush}"
                                Background="{StaticResource ControlAccentTranslucentFullBackgroundBrush}">
                            <StackPanel Margin="12,6">
                                <TextBlock
                                    Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                    Text="{x:Static lang:Resources.InstanceSetupView_VersionLabelText}" />
                                <TextBlock FontSize="{StaticResource LargeFontSize}"
                                           Text="{Binding Basic.Version, FallbackValue=Version}" />

                            </StackPanel>
                        </Border>
                        <Border BorderThickness="3,0,0,0" CornerRadius="{StaticResource SmallCornerRadius}"
                                BorderBrush="{StaticResource ControlBackgroundBrush}"
                                Background="{StaticResource ControlTranslucentFullBackgroundBrush}">
                            <DockPanel>
                                <Button DockPanel.Dock="Right" Padding="8,4" Command="{Binding EditLoaderCommand}"
                                        Background="{StaticResource ControlTranslucentFullBackgroundBrush}"
                                        IsVisible="{Binding Basic.Source,Converter={x:Static ObjectConverters.IsNull}}"
                                        CornerRadius="{Binding Source={StaticResource SmallCornerRadius},Converter={x:Static husk:CornerRadiusConverters.Right}}">
                                    <icons:PackIconLucide Kind="ArrowRightLeft" Height="{StaticResource LargeFontSize}"
                                                          Width="{StaticResource LargeFontSize}" />
                                </Button>
                                <StackPanel Margin="12,6">
                                    <TextBlock
                                        Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                        Text="{x:Static lang:Resources.InstanceSetupView_LoaderLabelText}" />
                                    <TextBlock FontSize="{StaticResource LargeFontSize}"
                                               Text="{Binding LoaderLabel, FallbackValue=None}" />
                                </StackPanel>
                            </DockPanel>
                        </Border>
                    </StackPanel>
                </Grid>
                <husk:LazyContainer Grid.Column="1" MinWidth="198"
                                    Source="{Binding Reference,FallbackValue={x:Null}}"
                                    IsVisible="{Binding Basic.Source,Converter={x:Static ObjectConverters.IsNotNull},FallbackValue=True}">
                    <husk:LazyContainer.BadContent>
                        <husk:Card BorderThickness="2"
                                   BorderBrush="{StaticResource ControlBorderBrush}">
                            <StackPanel>
                                <TextBlock
                                    Text="{x:Static lang:Resources.InstanceSetupView_ReferenceUnavailableLabelText}" />
                                <Button Content="Retry" IsVisible="False" />
                            </StackPanel>
                        </husk:Card>
                    </husk:LazyContainer.BadContent>
                    <husk:LazyContainer.SourceTemplate>
                        <DataTemplate x:DataType="m:InstanceReferenceModel">
                            <Border Background="{StaticResource ControlBackgroundBrush}"
                                    CornerRadius="{StaticResource MediumCornerRadius}" MaxWidth="320">
                                <controls:Plaque>
                                    <controls:Plaque.Header>
                                        <Grid Grid.Row="1" ColumnDefinitions="Auto,*,Auto"
                                              ColumnSpacing="4">
                                            <fi:SymbolIcon Grid.Column="0" Symbol="Info" Margin="6,0,0,0"
                                                           FontSize="{StaticResource SmallFontSize}" />
                                            <TextBlock Grid.Column="1"
                                                       Text="{Binding VersionName,FallbackValue=Version}"
                                                       VerticalAlignment="Center" TextTrimming="CharacterEllipsis"
                                                       ToolTip.Tip="{Binding VersionName}" />
                                            <Button Grid.Column="2"
                                                    Content="{x:Static lang:Resources.InstanceSetupView_SwitchVersionButtonText}"
                                                    Classes="Small"
                                                    Theme="{StaticResource OutlineButtonTheme}"
                                                    Command="{Binding $parent[v:InstanceSetupView].((vm:InstanceSetupViewModel)DataContext).CheckUpdateCommand, FallbackValue={x:Null}}" />
                                        </Grid>
                                    </controls:Plaque.Header>
                                    <Grid ColumnDefinitions="Auto,*,Auto" ColumnSpacing="8">
                                        <Border Grid.Column="0" Height="42" Width="42"
                                                CornerRadius="{StaticResource SmallCornerRadius}">
                                            <Border.Background>
                                                <ImageBrush
                                                    async:ImageBrushLoader.Source="{Binding Thumbnail,FallbackValue={x:Null}}"
                                                    Stretch="UniformToFill" />
                                            </Border.Background>
                                        </Border>
                                        <StackPanel Grid.Column="1" VerticalAlignment="Center" Spacing="2">
                                            <TextBlock Text="{Binding ProjectName,FallbackValue=Display}"
                                                       FontSize="{StaticResource LargeFontSize}"
                                                       TextTrimming="CharacterEllipsis"
                                                       FontWeight="{StaticResource ControlStrongFontWeight}"
                                                       ToolTip.Tip="{Binding ProjectName,FallbackValue=Name}" />
                                            <HyperlinkButton NavigateUri="{Binding SourceUrl}">
                                                <ToolTip.Tip>
                                                    <StackPanel Spacing="2">
                                                        <Grid ColumnDefinitions="*,Auto" ColumnSpacing="4">
                                                            <TextBlock Grid.Column="0"
                                                                       Text="{x:Static lang:Resources.Shared_ExternalLinkLabelText}"
                                                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                                       FontSize="{StaticResource SmallFontSize}" />
                                                            <fi:SymbolIcon Grid.Column="1" Symbol="Open"
                                                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                                           FontSize="{StaticResource SmallFontSize}" />
                                                        </Grid>
                                                        <TextBlock
                                                            Text="{Binding $parent[HyperlinkButton].NavigateUri}" />
                                                    </StackPanel>
                                                </ToolTip.Tip>
                                                <StackPanel Orientation="Horizontal" Spacing="4">
                                                    <TextBlock
                                                        Text="{Binding Label,FallbackValue=LABEL,Converter={x:Static husk:StringConverters.ToUpper}}" />
                                                    <fi:SymbolIcon Symbol="Open" FontSize="12" />
                                                </StackPanel>
                                            </HyperlinkButton>
                                        </StackPanel>
                                        <Button Grid.Column="2" CornerRadius="{StaticResource FullCornerRadius}"
                                                VerticalAlignment="Center" Padding="8"
                                                Command="{Binding $parent[v:InstanceSetupView].((vm:InstanceSetupViewModel)DataContext).ViewDetailsCommand, FallbackValue={x:Null}}">
                                            <fi:SymbolIcon Symbol="ArrowRight"
                                                           FontSize="{StaticResource LargeFontSize}" />
                                        </Button>
                                    </Grid>
                                </controls:Plaque>
                            </Border>
                        </DataTemplate>
                    </husk:LazyContainer.SourceTemplate>
                </husk:LazyContainer>
            </Grid>
            <husk:SwitchPresenter Grid.Row="2" Value="{Binding IsRefreshing}" TargetType="x:Boolean">
                <husk:SwitchCase Value="False">
                    <cp:PackageContainer LayoutIndex="{Binding LayoutIndex,Mode=TwoWay}" Items="{Binding Stage}"
                                         TotalCount="{Binding StageCount}"
                                         PrimaryCommand="{Binding ViewPackageCommand}"
                                         RemoveCommand="{Binding RemovePackageCommand}"
                                         GotoExplorerCommand="{Binding GotoPackageExplorerViewCommand}"
                                         ExportListCommand="{Binding ExportListCommand}"
                                         UpdateBatchCommand="{Binding UpdateBatchCommand}" />
                </husk:SwitchCase>
                <husk:SwitchCase Value="True">
                    <StackPanel VerticalAlignment="Center" Spacing="8">
                        <husk:ProgressRing Width="56" Height="56" Value="{Binding RefreshingCount,Mode=OneWay}"
                                           Maximum="{Binding StageCount}" />
                        <TextBlock HorizontalAlignment="Center">
                            <Run Text="{x:Static lang:Resources.InstanceSetupView_LoadingPackageLabelText}" />
                            <Run Text="(" Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                            <Run Text="{Binding RefreshingCount,FallbackValue=0}" />
                            <Run Text="/" Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                            <Run Text="{Binding StageCount,FallbackValue=514}" />
                            <Run Text=")" Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                        </TextBlock>
                    </StackPanel>
                </husk:SwitchCase>
            </husk:SwitchPresenter>
        </Grid>
    </husk:BusyContainer>
</controls:Subpage>