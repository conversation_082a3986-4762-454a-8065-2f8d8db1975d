﻿<husk:Page xmlns="https://github.com/avaloniaui"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
           xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
           xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
           xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
           xmlns:fi="using:FluentIcons.Avalonia"
           xmlns:vm="using:Polymerium.App.ViewModels"
           xmlns:v="using:Polymerium.App.Views"
           mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
           x:Class="Polymerium.App.Views.UnknownView" x:DataType="vm:UnknownViewModel">
    <husk:Page.Header>
        <DockPanel HorizontalSpacing="8">
            <StackPanel DockPanel.Dock="Right" Spacing="4" Orientation="Horizontal">
                <Button Command="{Binding DebugCommand}">
                    <icons:PackIconLucide Kind="Bug" Width="12" Height="12" />
                </Button>
                <Button Click="ThemeSwitchButton_OnClick">
                    <icons:PackIconLucide Kind="Lightbulb" Width="12" Height="12" />
                </Button>
            </StackPanel>
            <TextBlock Text="我能吞下玻璃而不伤身体" Theme="{StaticResource PageHeaderTextBlockTheme}" />
        </DockPanel>
    </husk:Page.Header>
    <husk:Page.Styles>
        <Style Selector=":loading">
            <Style Selector="^ ProgressBar#Bar">
                <Setter Property="IsIndeterminate" Value="False" />
            </Style>
            <Style Selector="^ ProgressBar#Baz">
                <Setter Property="IsIndeterminate" Value="True" />
            </Style>
            <Style Selector="^ husk|ProgressRing#Ring">
                <Setter Property="IsIndeterminate" Value="False" />
            </Style>
            <Style Selector="^ husk|ProgressRing#Zing">
                <Setter Property="IsIndeterminate" Value="True" />
            </Style>
        </Style>
        <Style Selector=":finished">
            <Style Selector="^ ProgressBar#Bar">
                <Setter Property="IsIndeterminate" Value="True" />
            </Style>
            <Style Selector="^ ProgressBar#Baz">
                <Setter Property="IsIndeterminate" Value="False" />
            </Style>
            <Style Selector="^ husk|ProgressRing#Ring">
                <Setter Property="IsIndeterminate" Value="True" />
            </Style>
            <Style Selector="^ husk|ProgressRing#Zing">
                <Setter Property="IsIndeterminate" Value="False" />
            </Style>
            <Style Selector="^ TextBlock#Block">
                <Setter Property="Text" Value="没有阴影的中文是没有阴影的。" />
            </Style>
        </Style>
    </husk:Page.Styles>
    <StackPanel Spacing="8">
        <TextBlock Text="{Binding Title}" />
        <TabStrip>
            <TabStripItem>
                <TextBlock>
                    <Run Text="Hub" FontSize="{StaticResource LargeFontSize}" />
                    <LineBreak />
                    <Run Text="Design Center" Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                </TextBlock>
            </TabStripItem>
            <TabStripItem IsEnabled="False">
                <TextBlock>
                    <Run Text="Dock" FontSize="{StaticResource LargeFontSize}" />
                    <LineBreak />
                    <Run Text="Machine Assembler" Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                </TextBlock>
            </TabStripItem>
            <TabStripItem>
                <TextBlock>
                    <Run Text="Socket" FontSize="{StaticResource LargeFontSize}" />
                    <LineBreak />
                    <Run Text="Power Supply" Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                </TextBlock>
            </TabStripItem>
        </TabStrip>
        <TabStrip Theme="{StaticResource SegmentedTabStripTheme}" HorizontalAlignment="Right"
                  Background="{StaticResource OverlayBackgroundBrush}">
            <TabStripItem>
                <icons:PackIconLucide Kind="Menu" Height="12" Width="12" />
            </TabStripItem>
            <TabStripItem>
                <icons:PackIconLucide Kind="Folder" Height="12" Width="12" />
            </TabStripItem>
        </TabStrip>
        <TabStrip Theme="{StaticResource SegmentedTabStripTheme}">
            <TabStripItem Content="Monday" />
            <TabStripItem Content="Tuesday" />
            <TabStripItem Content="Wednesday" />
            <TabStripItem Content="Thursday" />
            <TabStripItem Content="Friday" />
        </TabStrip>
        <TabControl BorderBrush="{StaticResource ControlBorderBrush}" BorderThickness="1"
                    Background="{StaticResource CardBackgroundBrush}"
                    CornerRadius="{StaticResource MediumCornerRadius}">
            <TabItem Header="Foo">
                <StackPanel Spacing="8">
                    <Button Theme="{StaticResource OutlineButtonTheme}" Content="Hello"
                            Command="{Binding HelloCommand}" />
                    <Button Classes="Primary" Content="World" Command="{Binding WorldCommand}" />
                    <Button Theme="{StaticResource OutlineButtonTheme}" Content="Butcher"
                            Command="{Binding ButcherCommand}" />
                    <HyperlinkButton Content="THIS A LINK!" />

                    <husk:NotificationItem x:Name="Notification" Actions="{Binding $parent[v:UnknownView].Actions}"
                                           Title="Downloading file..." Progress="54"
                                           IsProgressBarVisible="True">
                        <StackPanel Spacing="4">
                            <StackPanel Orientation="Horizontal" Spacing="6">
                                <fi:SymbolIcon Symbol="FolderZip" FontSize="{StaticResource MediumFontSize}" />
                                <TextBlock Text="四大名著.zip" />
                            </StackPanel>
                            <TextBlock Foreground="{StaticResource ControlSecondaryForegroundBrush}">
                                <Run Text="D: 128 B/s" />
                                <Run Text="E: 46 s" />
                                <Run Text="S: 5.4 MiB" />
                            </TextBlock>
                        </StackPanel>
                    </husk:NotificationItem>
                    <DockPanel>
                        <ComboBox DockPanel.Dock="Right" Name="ComboBox" PlaceholderText="这是一个一个">
                            <ComboBoxItem Content="这是一个 1" />
                            <ComboBoxItem Content="这是一个 2" />
                            <ComboBoxItem Content="这是一个 3" />
                            <ComboBoxItem Content="这是一个 4" />
                            <ComboBoxItem Content="这是一个 5" />
                            <ComboBoxItem Content="这是一个 6" />
                            <ComboBoxItem Content="这是一个 7" />
                            <ComboBoxItem Content="这是一个 8" />
                            <ComboBoxItem Content="这是一个 9" />
                            <ComboBoxItem Content="这是一个 0" />
                        </ComboBox>
                        <StackPanel Orientation="Horizontal" Spacing="4">
                            <ToggleSwitch OnContent="这是 ON" OffContent="这是 OFF"
                                          IsChecked="{Binding IsEnabled,ElementName=ComboBox}" />

                            <CheckBox IsThreeState="True" IsChecked="{Binding IsEnabled,ElementName=ComboBox}">
                                <TextBlock Text="Make it REAL" />
                            </CheckBox>
                        </StackPanel>
                    </DockPanel>
                    <husk:InfoBar Header="You know what?">
                        <TextBlock TextWrapping="WrapWithOverflow">
                            <Run
                                Text="Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet." />
                            <husk:HighlightBlock Text="Ctrl+V" Classes="Shortcut" />
                            <Run Text="Then" />
                            <husk:HighlightBlock Text="✨ git push -f" />
                            <Run Text="If ruined," />
                            <HyperlinkButton Content="Undo" />
                            <HyperlinkButton Content="Redo" IsEnabled="False" />
                            <Run
                                Text=".Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed di." />
                        </TextBlock>
                    </husk:InfoBar>
                </StackPanel>
            </TabItem>
            <TabItem Header="Bar">
                <StackPanel Spacing="12">
                    <ProgressBar x:Name="Bar" Value="50">
                        <ProgressBar.Styles>
                            <Style Selector="ProgressBar">
                                <Style.Animations>
                                    <Animation Duration="0:0:3" IterationCount="Infinite" Easing="QuadraticEaseOut">
                                        <KeyFrame Cue="0%">
                                            <Setter Property="Value" Value="0" />
                                        </KeyFrame>
                                        <KeyFrame Cue="50%">
                                            <Setter Property="Value" Value="100" />
                                        </KeyFrame>
                                        <KeyFrame Cue="100%">
                                            <Setter Property="Value" Value="0" />
                                        </KeyFrame>
                                    </Animation>
                                </Style.Animations>
                            </Style>
                        </ProgressBar.Styles>
                    </ProgressBar>
                    <ProgressBar x:Name="Baz" Theme="{StaticResource BorderedProgressBarTheme}" Value="50" />
                    <TextBlock x:Name="Block" FontSize="13" />
                    <StackPanel Orientation="Horizontal" Spacing="12">
                        <husk:ProgressRing x:Name="Ring" Value="50" Height="64" Width="64">
                            <husk:ProgressRing.Styles>
                                <Style Selector="husk|ProgressRing">
                                    <Style.Animations>
                                        <Animation Duration="0:0:3" IterationCount="Infinite"
                                                   Easing="QuadraticEaseOut">
                                            <KeyFrame Cue="0%">
                                                <Setter Property="Value" Value="0" />
                                            </KeyFrame>
                                            <KeyFrame Cue="50%">
                                                <Setter Property="Value" Value="100" />
                                            </KeyFrame>
                                            <KeyFrame Cue="100%">
                                                <Setter Property="Value" Value="0" />
                                            </KeyFrame>
                                        </Animation>
                                    </Style.Animations>
                                </Style>
                            </husk:ProgressRing.Styles>
                        </husk:ProgressRing>
                        <husk:ProgressRing x:Name="Zing" Value="50" Height="64" Width="64" />
                        <husk:ProgressRing TrackPadding="6" TrackStrokeWidth="4"
                                           Theme="{StaticResource SolidProgressRingTheme}" Value="50" Height="64"
                                           Width="64">
                            <husk:ProgressRing.Styles>
                                <Style Selector="husk|ProgressRing">
                                    <Style.Animations>
                                        <Animation Duration="0:0:3" IterationCount="Infinite"
                                                   Easing="QuadraticEaseOut">
                                            <KeyFrame Cue="0%">
                                                <Setter Property="Value" Value="0" />
                                            </KeyFrame>
                                            <KeyFrame Cue="50%">
                                                <Setter Property="Value" Value="100" />
                                            </KeyFrame>
                                            <KeyFrame Cue="100%">
                                                <Setter Property="Value" Value="0" />
                                            </KeyFrame>
                                        </Animation>
                                    </Style.Animations>
                                </Style>
                            </husk:ProgressRing.Styles>
                        </husk:ProgressRing>
                        <husk:ProgressRing TrackPadding="6" TrackStrokeWidth="4"
                                           Theme="{StaticResource SolidProgressRingTheme}" Value="50" Height="64"
                                           Width="64" IsIndeterminate="True" />
                        <Arc Height="64" Width="64" Fill="Yellow" SweepAngle="270" StartAngle="-90" />
                    </StackPanel>
                    <ListBox>
                        <ListBoxItem Content="这是一个 1" />
                        <ListBoxItem Content="这是一个 2" />
                        <ListBoxItem Content="这是一个 3" />
                        <ListBoxItem Content="这是一个 4" />
                        <ListBoxItem Content="这是一个 5" />
                        <ListBoxItem Content="这是一个 6" />
                        <ListBoxItem Content="这是一个 7" />
                        <ListBoxItem Content="这是一个 8" />
                        <ListBoxItem Content="这是一个 9" />
                        <ListBoxItem Content="这是一个 0" />
                    </ListBox>
                </StackPanel>
            </TabItem>
            <TabItem Header="Baz">
                <StackPanel Spacing="12">
                    <husk:Card Padding="0">
                        <TabControl BorderBrush="{StaticResource ControlBorderBrush}" BorderThickness="0,1,0,0">
                            <TabControl.Styles>
                                <Style Selector="TabItem">
                                    <Setter Property="Margin" Value="0" />
                                </Style>
                            </TabControl.Styles>
                            <TabItem Header="Description">
                                <StackPanel>
                                    <Border Background="{StaticResource ControlTranslucentFullBackgroundBrush}"
                                            HorizontalAlignment="Left"
                                            CornerRadius="{StaticResource SmallCornerRadius}">
                                        <Grid ColumnDefinitions="Auto,0,Auto,0,Auto">
                                            <Button Grid.Column="0" Theme="{StaticResource GhostButtonTheme}"
                                                    Classes="Small"
                                                    CornerRadius="{Binding Source={StaticResource SmallCornerRadius},Converter={x:Static husk:CornerRadiusConverters.Left}}">
                                                <StackPanel Orientation="Horizontal" Spacing="4">
                                                    <fi:SymbolIcon Symbol="Calendar"
                                                                   FontSize="{StaticResource MediumFontSize}" />
                                                    <TextBlock Text="November 10, 2023" />
                                                </StackPanel>
                                            </Button>
                                            <husk:Divider Grid.Column="2" Margin="0,4" Orientation="Vertical" />
                                            <Button Grid.Column="4" Theme="{StaticResource GhostButtonTheme}"
                                                    Classes="Small"
                                                    CornerRadius="{Binding Source={StaticResource SmallCornerRadius},Converter={x:Static husk:CornerRadiusConverters.Right}}">
                                                <StackPanel Orientation="Horizontal" Spacing="4">
                                                    <fi:SymbolIcon Symbol="Clock"
                                                                   FontSize="{StaticResource MediumFontSize}" />
                                                    <TextBlock Text="18:13 PM" />
                                                </StackPanel>
                                            </Button>
                                        </Grid>
                                    </Border>
                                </StackPanel>
                            </TabItem>
                            <TabItem Header="Versions" />
                        </TabControl>
                    </husk:Card>
                </StackPanel>
            </TabItem>
        </TabControl>
    </StackPanel>
</husk:Page>