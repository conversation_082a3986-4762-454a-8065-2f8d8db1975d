﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <ControlTheme x:Key="{x:Type local:OverlayItem}" TargetType="local:OverlayItem">
        <Setter Property="RenderTransformOrigin" Value="50%,50%" />
        <Setter Property="VerticalAlignment" Value="{Binding $parent[local:OverlayHost].VerticalContentAlignment}" />
        <Setter Property="HorizontalAlignment" Value="{Binding $parent[local:OverlayHost].HorizontalContentAlignment}" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="Transitions">
            <Transitions>
                <DoubleTransition Property="Opacity"
                                  Duration="{StaticResource ControlFasterAnimationDuration}" />
                <TransformOperationsTransition Property="RenderTransform"
                                               Delay="{StaticResource ControlFastestAnimationDuration}"
                                               Duration="{StaticResource ControlFasterAnimationDuration}" />
            </Transitions>
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border Name="PART_ScaleRoot">
                    <Border.Transitions>
                        <Transitions>
                            <TransformOperationsTransition Property="RenderTransform"
                                                           Delay="{StaticResource ControlFastestAnimationDuration}"
                                                           Duration="{StaticResource ControlFasterAnimationDuration}" />
                        </Transitions>
                    </Border.Transitions>
                    <ContentPresenter Name="{x:Static local:OverlayItem.PART_ContentPresenter}"
                                      Content="{TemplateBinding Content}"
                                      Background="{TemplateBinding Background}"
                                      Padding="{TemplateBinding Padding}"
                                      BackgroundSizing="{TemplateBinding BackgroundSizing}" />
                </Border>
            </ControlTemplate>
        </Setter>

        <!-- <Style Selector="^:active"> -->
        <!--     <Setter Property="Effect"> -->
        <!--         <DropShadowEffect OffsetX="0" OffsetY="0" BlurRadius="12" Opacity="0.2" /> -->
        <!--     </Setter> -->
        <!-- </Style> -->

        <Style Selector="^">
            <Setter Property="Opacity" Value="0.0" />

            <Setter Property="RenderTransform" Value="translateY(-30px)" />

            <!-- <Setter Property="Effect"> -->
            <!--     <BlurEffect Radius="6" /> -->
            <!-- </Setter> -->

            <Style Selector="^ /template/ Border#PART_ScaleRoot" />

            <Style Selector="^ /template/ Border#PART_ScaleRoot">
                <Setter Property="RenderTransform" Value="scaleX(0.90)" />
            </Style>
        </Style>

        <Style Selector="^[Distance=0]">
            <Setter Property="Opacity" Value="1.0" />

            <Setter Property="RenderTransform" Value="translateY(0px)" />

            <Style Selector="^ /template/ Border#PART_ScaleRoot">
                <Setter Property="RenderTransform" Value="scaleX(1.0)" />
            </Style>
        </Style>
        <Style Selector="^[Distance=1]">
            <Setter Property="Opacity" Value="0.8" />
            <Setter Property="IsEnabled" Value="False" />

            <Setter Property="RenderTransform" Value="translateY(-6px)" />

            <Style Selector="^ /template/ Border#PART_ScaleRoot">
                <Setter Property="RenderTransform" Value="scaleX(0.98)" />
            </Style>
        </Style>
        <Style Selector="^[Distance=2]">
            <Setter Property="Opacity" Value="0.6" />
            <Setter Property="IsEnabled" Value="False" />

            <Setter Property="RenderTransform" Value="translateY(-12px)" />

            <Style Selector="^ /template/ Border#PART_ScaleRoot">
                <Setter Property="RenderTransform" Value="scaleX(0.96)" />
            </Style>
        </Style>
        <Style Selector="^[Distance=3]">
            <Setter Property="Opacity" Value="0.4" />
            <Setter Property="IsEnabled" Value="False" />

            <Setter Property="RenderTransform" Value="translateY(-18px)" />

            <Style Selector="^ /template/ Border#PART_ScaleRoot">
                <Setter Property="RenderTransform" Value="scaleX(0.94)" />
            </Style>
        </Style>
        <Style Selector="^[Distance=4]">
            <Setter Property="Opacity" Value="0.2" />
            <Setter Property="IsEnabled" Value="False" />

            <Setter Property="RenderTransform" Value="translateY(-24px)" />

            <Style Selector="^ /template/ Border#PART_ScaleRoot">
                <Setter Property="RenderTransform" Value="scaleX(0.92)" />
            </Style>
        </Style>
    </ControlTheme>
</ResourceDictionary>