﻿<husk:Modal xmlns="https://github.com/avaloniaui"
            xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
            xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
            xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
            xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
            xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
            xmlns:modals="clr-namespace:Polymerium.App.Modals"
            xmlns:m="clr-namespace:Polymerium.App.Models"
            xmlns:async="clr-namespace:AsyncImageLoader;assembly=AsyncImageLoader.Avalonia"
            xmlns:assets="clr-namespace:Polymerium.App.Assets"
            xmlns:accounts="clr-namespace:Polymerium.Trident.Accounts;assembly=Polymerium.Trident"
            xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
            mc:Ignorable="d" Background="Transparent" Width="280"
            x:Class="Polymerium.App.Modals.AccountEntryModal" x:DataType="m:AccountModel">
    <Grid RowDefinitions="Auto,Auto,*" ColumnSpacing="12">
        <Border Grid.Row="1" Grid.RowSpan="2" CornerRadius="{StaticResource MediumCornerRadius}"
                Background="{StaticResource FlyoutBackgroundBrush}">
            <StackPanel Margin="{StaticResource ModalContentMargin}" Spacing="24">
                <StackPanel Spacing="12">
                    <Panel>
                        <Button DockPanel.Dock="Right" CornerRadius="{StaticResource FullCornerRadius}"
                                HorizontalAlignment="Right" Classes="Small" IsCancel="True"
                                Command="{Binding $parent[modals:AccountEntryModal].DismissCommand}">
                            <fi:SymbolIcon Symbol="Dismiss" FontSize="{StaticResource MediumFontSize}"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center" />
                        </Button>
                    </Panel>
                    <TextBlock Text="{Binding UserName,FallbackValue=Name}" HorizontalAlignment="Center"
                               FontWeight="{StaticResource ControlStrongFontWeight}"
                               FontSize="{StaticResource LargeFontSize}" />
                </StackPanel>
                <StackPanel Spacing="4">
                    <DockPanel>
                        <TextBlock DockPanel.Dock="Right"
                                   Text="{Binding EnrolledAtRaw,StringFormat={}{0:g},FallbackValue=2025/12/13 02:03}" />
                        <TextBlock Text="{x:Static lang:Resources.AccountEntryModal_EnrolledLabelText}"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}" />

                    </DockPanel>
                    <DockPanel>
                        <TextBlock DockPanel.Dock="Right"
                                   Text="{Binding LastUsedAt,FallbackValue=never}" />
                        <TextBlock Text="{x:Static lang:Resources.AccountEntryModal_LastUsedLabelText}"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </DockPanel>
                </StackPanel>
                <Border CornerRadius="{StaticResource SmallCornerRadius}"
                        BorderBrush="{StaticResource ControlAccentInteractiveBorderBrush}" BorderThickness="1"
                        Padding="12,8"
                        Background="{StaticResource ControlAccentTranslucentHalfBackgroundBrush}">
                    <husk:SwitchPresenter Value="{Binding Type}">
                        <husk:SwitchCase Value="{x:Type accounts:TrialAccount}">
                            <StackPanel Spacing="8">
                                <TextBlock Text="{Binding TypeName,FallbackValue=Family}"
                                           FontSize="{StaticResource LargeFontSize}" />
                                <StackPanel>
                                    <TextBlock
                                        Text="{x:Static lang:Resources.AccountEntryModal_TrialPrompt}"
                                        TextWrapping="Wrap"
                                        Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                </StackPanel>
                            </StackPanel>
                        </husk:SwitchCase>
                        <husk:SwitchCase Value="{x:Type accounts:OfflineAccount}">
                            <StackPanel Spacing="8">
                                <TextBlock Text="{Binding TypeName,FallbackValue=Offline}"
                                           FontSize="{StaticResource LargeFontSize}" />
                                <StackPanel>
                                    <TextBlock
                                        Text="{x:Static lang:Resources.AccountEntryModal_OfflinePrompt}"
                                        TextWrapping="Wrap"
                                        Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                </StackPanel>
                            </StackPanel>
                        </husk:SwitchCase>
                        <husk:SwitchCase Value="{x:Type accounts:MicrosoftAccount}">
                            <StackPanel Spacing="8">
                                <TextBlock Text="{Binding TypeName,FallbackValue=Microsoft}"
                                           FontSize="{StaticResource LargeFontSize}" />
                                <StackPanel>
                                    <TextBlock
                                        Text="{x:Static lang:Resources.AccountEntryModal_MicrosoftPrompt}"
                                        TextWrapping="Wrap"
                                        Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                </StackPanel>
                                <StackPanel Orientation="Horizontal" Spacing="8" HorizontalAlignment="Right"
                                            IsVisible="False">
                                    <StackPanel Orientation="Horizontal" Spacing="4">
                                        <fi:SymbolIcon Symbol="CheckmarkCircle"
                                                       FontSize="{StaticResource MediumFontSize}"
                                                       IconVariant="Filled" VerticalAlignment="Center"
                                                       Foreground="{StaticResource ControlSuccessForegroundBrush}" />
                                        <TextBlock Text="Refreshed" VerticalAlignment="Center"
                                                   Foreground="{StaticResource ControlSuccessForegroundBrush}" />
                                    </StackPanel>
                                    <Button HorizontalAlignment="Right" Content="Refresh" Classes="Primary Small" />
                                </StackPanel>
                            </StackPanel>
                        </husk:SwitchCase>
                    </husk:SwitchPresenter>
                </Border>
            </StackPanel>
        </Border>
        <Border Grid.Row="0" Grid.RowSpan="2" HorizontalAlignment="Center"
                CornerRadius="{StaticResource LargeCornerRadius}"
                Background="{StaticResource FlyoutBackgroundBrush}" Padding="8" BoxShadow="0 0 4 0 #3F000000">
            <Border Width="64" Height="64" CornerRadius="{StaticResource SmallCornerRadius}">
                <Border.Background>
                    <ImageBrush
                        async:ImageBrushLoader.Source="{Binding FaceUrl,FallbackValue={x:Static assets:AssetUriIndex.STEVE_FACE_IMAGE}}" />
                </Border.Background>
            </Border>
        </Border>
    </Grid>
</husk:Modal>