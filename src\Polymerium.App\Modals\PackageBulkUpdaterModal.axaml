﻿<husk:Modal xmlns="https://github.com/avaloniaui"
            xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
            xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
            xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
            xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
            xmlns:modals="clr-namespace:Polymerium.App.Modals"
            xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
            xmlns:m="clr-namespace:Polymerium.App.Models"
            xmlns:async="clr-namespace:AsyncImageLoader;assembly=AsyncImageLoader.Avalonia"
            xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
            mc:Ignorable="d"
            x:Class="Polymerium.App.Modals.PackageBulkUpdaterModal">
    <Grid RowDefinitions="Auto,*,Auto" RowSpacing="24">
        <TextBlock Grid.Row="0" Text="{x:Static lang:Resources.PackageBulkUpdaterModal_Title}"
                   FontSize="{StaticResource LargeFontSize}" FontWeight="{StaticResource ControlStrongFontWeight}" />
        <Panel Grid.Row="1">
            <StackPanel VerticalAlignment="Center"
                        Spacing="8"
                        IsVisible="{Binding $parent[modals:PackageBulkUpdaterModal].Updates.Count,Converter={x:Static husk:NumberConverters.IsZero},FallbackValue=True}">
                <icons:PackIconLucide Kind="Package" Height="{StaticResource ExtraLargeFontSize}"
                                      Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                      Width="{StaticResource ExtraLargeFontSize}" HorizontalAlignment="Center" />
                <TextBlock Text="{x:Static lang:Resources.Shared_EmptyListLabelText}"
                           FontSize="{StaticResource LargeFontSize}"
                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                           HorizontalAlignment="Center" />
            </StackPanel>
            <ScrollViewer Margin="-18,0" Padding="18,0">
                <ItemsControl ItemsSource="{Binding $parent[modals:PackageBulkUpdaterModal].Updates}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate DataType="m:PackageUpdaterModel">
                            <Border Padding="12" Margin="3" BoxShadow="0 0 4 0 #3F000000"
                                    CornerRadius="{StaticResource MediumCornerRadius}"
                                    Background="{StaticResource OverlaySolidBackgroundBrush}">
                                <Grid ColumnDefinitions="Auto,*,Auto" ColumnSpacing="12">
                                    <Border Grid.Column="0" Width="{Binding $self.Bounds.Height}"
                                            CornerRadius="{StaticResource SmallCornerRadius}"
                                            ToolTip.Tip="{Binding Package.ProjectName}">
                                        <Border.Background>
                                            <ImageBrush async:ImageBrushLoader.Source="{Binding Thumbnail}" />
                                        </Border.Background>
                                    </Border>
                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="{Binding NewVersionName}"
                                                   FontWeight="{StaticResource ControlStrongFontWeight}"
                                                   Foreground="{StaticResource ControlAccentForegroundBrush}"
                                                   TextTrimming="CharacterEllipsis" />
                                        <TextBlock Text="{Binding NewVersionTime}"
                                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                   FontSize="{StaticResource SmallFontSize}"
                                                   TextTrimming="CharacterEllipsis" />
                                        <TextBlock Text="{Binding OldVersionName}"
                                                   FontWeight="{StaticResource ControlStrongFontWeight}"
                                                   TextTrimming="CharacterEllipsis" />
                                        <TextBlock Text="{Binding OldVersionTime}"
                                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                   FontSize="{StaticResource SmallFontSize}"
                                                   TextTrimming="CharacterEllipsis" />
                                    </StackPanel>
                                    <Grid Grid.Column="2" RowDefinitions="*,*">
                                        <CheckBox Grid.Row="0" VerticalAlignment="Center"
                                                  HorizontalAlignment="Right"
                                                  IsChecked="{Binding IsChecked,Mode=TwoWay}" />
                                        <icons:PackIconLucide Grid.Row="1" VerticalAlignment="Center"
                                                              Kind="CornerUpLeft"
                                                              Height="{StaticResource MediumFontSize}"
                                                              Width="{StaticResource MediumFontSize}" />
                                    </Grid>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </Panel>
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Spacing="8">
            <Button Classes="Primary"
                    Command="{Binding $parent[modals:PackageBulkUpdaterModal].UpdateCommand}">
                <husk:IconLabel Icon="BoxMultiple"
                                Text="{x:Static lang:Resources.PackageBulkUpdaterModal_UpdateButtonText}" Spacing="8" />
            </Button>
            <Button Command="{Binding $parent[modals:PackageBulkUpdaterModal].DismissCommand}">
                <TextBlock Text="{x:Static lang:Resources.Dialog_DismissButtonText}" />
            </Button>
        </StackPanel>
    </Grid>
</husk:Modal>