﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:Polymerium.App.Controls"
                    xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                    xmlns:async="clr-namespace:AsyncImageLoader;assembly=AsyncImageLoader.Avalonia"
                    xmlns:m="clr-namespace:Polymerium.App.Models"
                    xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
                    xmlns:v="clr-namespace:Polymerium.App.Views"
                    xmlns:vm="clr-namespace:Polymerium.App.ViewModels"
                    xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
                    xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages">
    <Design.PreviewWith>
        <Panel Background="White">
            <StackPanel Margin="24">
                <controls:ExhibitModpackButton Theme="{StaticResource GridExhibitModpackButtonTheme}" MaxWidth="256" />
            </StackPanel>
        </Panel>
    </Design.PreviewWith>

    <ControlTheme x:Key="BaseExhibitModpackButtonTheme" TargetType="controls:ExhibitModpackButton">
        <Setter Property="Cursor" Value="Hand" />
    </ControlTheme>

    <ControlTheme x:Key="ListExhibitModpackButtonTheme" TargetType="controls:ExhibitModpackButton"
                  BasedOn="{StaticResource BaseExhibitModpackButtonTheme}">
        <Setter Property="Template">
            <ControlTemplate x:DataType="m:ExhibitModel">
                <husk:Card Name="Background"
                           CornerRadius="{StaticResource MediumCornerRadius}"
                           Background="{StaticResource LayerBackgroundBrush}">
                    <husk:Card.Transitions>
                        <Transitions>
                            <BrushTransition Property="Background" Easing="SineEaseOut"
                                             Duration="{StaticResource ControlFasterAnimationDuration}" />
                            <BrushTransition Property="BorderBrush" Easing="SineEaseOut"
                                             Duration="{StaticResource ControlFasterAnimationDuration}" />
                            <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                              Duration="{StaticResource ControlFasterAnimationDuration}" />
                        </Transitions>
                    </husk:Card.Transitions>
                    <Grid RowDefinitions="*,Auto,Auto" ColumnDefinitions="Auto,*,Auto" RowSpacing="12"
                          ColumnSpacing="8">
                        <Border Grid.Row="0" Grid.Column="0"
                                CornerRadius="{StaticResource SmallCornerRadius}"
                                Width="{Binding $self.Bounds.Height}">
                            <Border.Background>
                                <ImageBrush Stretch="UniformToFill"
                                            async:ImageBrushLoader.Source="{Binding Thumbnail}" />
                            </Border.Background>
                        </Border>
                        <StackPanel Grid.Row="0" Grid.Column="1">
                            <TextBlock Text="{Binding ProjectName}" FontSize="{StaticResource LargeFontSize}"
                                       FontWeight="{StaticResource ControlStrongFontWeight}" />
                            <TextBlock Text="{Binding Summary}" MaxLines="2" TextWrapping="Wrap"
                                       TextTrimming="CharacterEllipsis" />
                        </StackPanel>
                        <StackPanel Grid.Row="0" Grid.Column="2" Spacing="4">
                            <StackPanel Orientation="Horizontal" Spacing="4" HorizontalAlignment="Right">
                                <icons:PackIconLucide Kind="CircleUser" Height="12" VerticalAlignment="Center"
                                                      Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                <TextBlock Text="{Binding Author}"
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                           ToolTip.Tip="{Binding Author}" />
                            </StackPanel>
                            <StackPanel Orientation="Horizontal" Spacing="4" HorizontalAlignment="Right">
                                <icons:PackIconLucide Kind="CircleArrowDown" Height="12"
                                                      VerticalAlignment="Center"
                                                      Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                <TextBlock Text="{Binding Downloads}"
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                           ToolTip.Tip="{Binding DownloadsRaw}" />
                            </StackPanel>
                            <StackPanel Orientation="Horizontal" Spacing="4" HorizontalAlignment="Right">
                                <icons:PackIconLucide Kind="Clock"
                                                      Height="12" VerticalAlignment="Center"
                                                      Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                <TextBlock Text="{Binding UpdatedAt}"
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                           ToolTip.Tip="{Binding UpdatedAtRaw}" />
                            </StackPanel>
                        </StackPanel>
                        <husk:Divider Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="3" />
                        <DockPanel Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="3">
                            <StackPanel DockPanel.Dock="Right" Orientation="Horizontal" Spacing="8">
                                <Button Classes="Primary Small"
                                        Command="{Binding $parent[v:MarketplaceSearchView].((vm:MarketplaceSearchViewModel)DataContext).InstallLatestCommand, FallbackValue={x:Null}}"
                                        CommandParameter="{Binding}">
                                    <StackPanel Orientation="Horizontal" Spacing="6">
                                        <!-- <icons:PackIconLucide Kind="Download" -->
                                        <!--                       Height="10" Width="10" VerticalAlignment="Center" /> -->
                                        <fi:SymbolIcon Symbol="ArrowDownload"
                                                       FontSize="{StaticResource SmallFontSize}" />
                                        <TextBlock
                                            Text="{x:Static lang:Resources.ExhibitModpackButton_InstallButtonText}" />
                                    </StackPanel>
                                </Button>
                                <Button Classes="Small" Flyout="{TemplateBinding ContextFlyout}">
                                    <!-- <icons:PackIconLucide Kind="Ellipsis" Height="8" Width="10" -->
                                    <!--                       VerticalAlignment="Center" /> -->
                                    <fi:SymbolIcon Symbol="MoreHorizontal"
                                                   FontSize="{StaticResource SmallFontSize}" />
                                </Button>
                            </StackPanel>
                            <ItemsControl ItemsSource="{Binding Tags}">
                                <ItemsControl.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <StackPanel Orientation="Horizontal" Spacing="4" />
                                    </ItemsPanelTemplate>
                                </ItemsControl.ItemsPanel>
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate x:DataType="x:String">
                                        <husk:Tag>
                                            <TextBlock Text="{Binding}"
                                                       FontSize="{StaticResource SmallFontSize}" />
                                        </husk:Tag>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </DockPanel>
                    </Grid>
                </husk:Card>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover /template/ husk|Card#Background">
            <Setter Property="BorderBrush"
                    Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
        </Style>

        <Style Selector="^:pressed /template/ husk|Card#Background">
            <Setter Property="Background"
                    Value="{StaticResource OverlayFullBackgroundBrush}" />
        </Style>

        <Style Selector="^:disabled /template/ husk|Card#Background">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="GridExhibitModpackButtonTheme" TargetType="controls:ExhibitModpackButton"
                  BasedOn="{StaticResource BaseExhibitModpackButtonTheme}">
        <Setter Property="Template">
            <ControlTemplate x:DataType="m:ExhibitModel">
                <husk:Card Name="Background"
                           CornerRadius="{StaticResource MediumCornerRadius}" Padding="0"
                           Background="{StaticResource LayerBackgroundBrush}"
                           BackgroundSizing="InnerBorderEdge">
                    <husk:Card.Transitions>
                        <Transitions>
                            <BrushTransition Property="Background" Easing="SineEaseOut"
                                             Duration="{StaticResource ControlFasterAnimationDuration}" />
                            <BrushTransition Property="BorderBrush" Easing="SineEaseOut"
                                             Duration="{StaticResource ControlFasterAnimationDuration}" />
                            <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                              Duration="{StaticResource ControlFasterAnimationDuration}" />
                        </Transitions>
                    </husk:Card.Transitions>
                    <Grid RowDefinitions="Auto,Auto,Auto,*,Auto,Auto">
                        <Grid Grid.Row="0" ColumnDefinitions="Auto,*"
                              Margin="12" ColumnSpacing="8">
                            <Border Grid.Column="0" Height="70" Width="70"
                                    CornerRadius="{StaticResource SmallCornerRadius}">
                                <Border.Background>
                                    <ImageBrush Stretch="UniformToFill"
                                                async:ImageBrushLoader.Source="{Binding Thumbnail}" />
                                </Border.Background>
                            </Border>
                            <Grid Grid.Column="1" RowDefinitions="*,Auto" RowSpacing="4">
                                <TextBlock Grid.Row="0"
                                           Text="{Binding ProjectName,FallbackValue=Name}"
                                           FontWeight="{StaticResource ControlStrongFontWeight}"
                                           FontSize="{StaticResource LargeFontSize}"
                                           TextTrimming="CharacterEllipsis" ToolTip.Tip="{Binding ProjectName}"
                                           TextWrapping="Wrap" MaxLines="2" />
                                <Grid Grid.Row="1" ColumnDefinitions="*,Auto" ColumnSpacing="8">
                                    <Button Grid.Column="0"
                                            Classes="Primary Small"
                                            Command="{Binding $parent[v:MarketplaceSearchView].((vm:MarketplaceSearchViewModel)DataContext).InstallLatestCommand, FallbackValue={x:Null}}"
                                            CommandParameter="{Binding}"
                                            BackgroundSizing="InnerBorderEdge">
                                        <StackPanel Orientation="Horizontal" Spacing="12">
                                            <fi:SymbolIcon Symbol="ArrowDownload"
                                                           FontSize="{StaticResource SmallFontSize}" />
                                            <TextBlock
                                                Text="{x:Static lang:Resources.ExhibitModpackButton_InstallButtonText}" />
                                        </StackPanel>
                                    </Button>
                                    <Button Grid.Column="1"
                                            Classes="Small"
                                            Flyout="{TemplateBinding ContextFlyout}">
                                        <fi:SymbolIcon Symbol="MoreHorizontal"
                                                       FontSize="{StaticResource SmallFontSize}" />
                                    </Button>
                                </Grid>
                            </Grid>
                        </Grid>
                        <Border Grid.Row="1" ToolTip.Tip="{Binding Summary}">
                            <TextBlock Text="{Binding Summary,FallbackValue=Summmmmmmmmmmmmmmmmmmmmmmmmmary}"
                                       MaxLines="2" TextWrapping="Wrap" TextTrimming="CharacterEllipsis"
                                       Margin="12,0" />
                        </Border>
                        <Border Grid.Row="2">
                            <!-- Filling Spare Space -->
                        </Border>
                        <ItemsControl Grid.Row="3" ItemsSource="{Binding Tags}" Margin="12"
                                      Background="Transparent">
                            <ToolTip.Tip>
                                <ItemsControl ItemsSource="{Binding Tags}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <StackPanel Spacing="4" />
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate x:DataType="x:String">
                                            <TextBlock Text="{Binding}" />
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ToolTip.Tip>
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel Orientation="Horizontal" ItemSpacing="4" LineSpacing="4" />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate x:DataType="x:String">
                                    <husk:Tag>
                                        <TextBlock Text="{Binding}"
                                                   FontSize="{StaticResource SmallFontSize}" />
                                    </husk:Tag>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                        <husk:Divider Grid.Row="4" />
                        <Grid Grid.Row="5" Margin="12,8" ColumnDefinitions="Auto,*,Auto,Auto,Auto,Auto"
                              ColumnSpacing="4">
                            <icons:PackIconLucide Grid.Column="0" Kind="CircleUser" Height="12"
                                                  VerticalAlignment="Center"
                                                  Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                            <TextBlock Grid.Column="1" Text="{Binding Author}" TextTrimming="CharacterEllipsis"
                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                       ToolTip.Tip="{Binding Author}" />
                            <icons:PackIconLucide Grid.Column="2" Kind="CircleArrowDown" Height="12"
                                                  VerticalAlignment="Center"
                                                  Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                            <TextBlock Grid.Column="3" Text="{Binding Downloads}"
                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                       ToolTip.Tip="{Binding DownloadsRaw}" />
                            <icons:PackIconLucide Grid.Column="4" Kind="Clock"
                                                  Height="12" VerticalAlignment="Center"
                                                  Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                            <TextBlock Grid.Column="5" Text="{Binding UpdatedAt}"
                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                       ToolTip.Tip="{Binding UpdatedAtRaw}" />
                        </Grid>
                    </Grid>
                </husk:Card>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover /template/ husk|Card#Background">
            <Setter Property="BorderBrush"
                    Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
        </Style>

        <Style Selector="^:pressed /template/ husk|Card#Background">
            <Setter Property="Background"
                    Value="{StaticResource OverlayFullBackgroundBrush}" />
        </Style>

        <Style Selector="^:disabled /template/ husk|Card#Background">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>