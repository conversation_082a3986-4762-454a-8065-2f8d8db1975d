﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ControlTheme x:Key="{x:Type ScrollViewer}"
                  TargetType="ScrollViewer">
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel>
                    <ScrollContentPresenter Name="PART_ContentPresenter"
                                            Padding="{TemplateBinding Padding}"
                                            ClipToBounds="{TemplateBinding ClipToBounds}"
                                            HorizontalSnapPointsType="{TemplateBinding HorizontalSnapPointsType}"
                                            VerticalSnapPointsType="{TemplateBinding VerticalSnapPointsType}"
                                            HorizontalSnapPointsAlignment="{TemplateBinding HorizontalSnapPointsAlignment}"
                                            VerticalSnapPointsAlignment="{TemplateBinding VerticalSnapPointsAlignment}"
                                            Background="{TemplateBinding Background}"
                                            ScrollViewer.IsScrollInertiaEnabled="{TemplateBinding IsScrollInertiaEnabled}"
                                            ScrollViewer.BringIntoViewOnFocusChange="{TemplateBinding BringIntoViewOnFocusChange}">
                        <ScrollContentPresenter.GestureRecognizers>
                            <ScrollGestureRecognizer
                                CanHorizontallyScroll="{Binding CanHorizontallyScroll, ElementName=PART_ContentPresenter}"
                                CanVerticallyScroll="{Binding CanVerticallyScroll, ElementName=PART_ContentPresenter}"
                                IsScrollInertiaEnabled="{Binding (ScrollViewer.IsScrollInertiaEnabled), ElementName=PART_ContentPresenter}" />
                        </ScrollContentPresenter.GestureRecognizers>
                    </ScrollContentPresenter>
                    <ScrollBar Name="PART_HorizontalScrollBar"
                               Orientation="Horizontal" VerticalAlignment="Bottom" />
                    <ScrollBar Name="PART_VerticalScrollBar"
                               Orientation="Vertical" HorizontalAlignment="Right" />
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^[AllowAutoHide=True]:not(:pointerover) /template/ ScrollBar:not(:focus-within)">
            <Setter Property="Opacity" Value="0" />
        </Style>
    </ControlTheme>
</ResourceDictionary>