﻿<husk:Dialog xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Polymerium.App.Dialogs.ModpackExporterDialog">
    <StackPanel>
        <TabStrip>
            <TabStripItem>
                <StackPanel>
                    <TextBlock Text="Tripack" FontSize="{StaticResource LargeFontSize}" />
                    <TextBlock
                        Text="Full power infused modpack format with maximized compatibility in TridentCore™ ecosystem."
                        Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                </StackPanel>
            </TabStripItem>
            <TabStripItem>
                <StackPanel>
                    <TextBlock Text="Packwiz" FontSize="{StaticResource LargeFontSize}" />
                    <TextBlock
                        Text="Intermediate modpack format with the ability to be converted into other formats on its official website."
                        Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                </StackPanel>
            </TabStripItem>
        </TabStrip>
    </StackPanel>
</husk:Dialog>