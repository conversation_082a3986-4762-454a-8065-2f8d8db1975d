﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="using:Polymerium.App.Controls"
                    xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                    xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
                    xmlns:m="using:Polymerium.App.Models"
                    xmlns:converters="using:Polymerium.App.Converters"
                    xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
                    xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages">
    <Design.PreviewWith>
        <Panel>
            <StackPanel Margin="24" Spacing="12">
                <local:InstancePackageButton />
            </StackPanel>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="BasePackageEntryButtonTheme" TargetType="local:InstancePackageButton">
        <Setter Property="Cursor" Value="Hand" />
    </ControlTheme>

    <ControlTheme x:Key="ListPackageEntryButtonTheme" TargetType="local:InstancePackageButton"
                  BasedOn="{StaticResource BasePackageEntryButtonTheme}">
        <Setter Property="Template">
            <ControlTemplate x:DataType="m:InstancePackageModel">
                <husk:Card Name="Background" Height="68" Background="{StaticResource LayerBackgroundBrush}"
                           CornerRadius="{StaticResource MediumCornerRadius}">
                    <husk:Card.Opacity>
                        <MultiBinding Converter="{x:Static converters:InternalConverters.OneOr}">
                            <Binding Path="IsEnabled" Mode="OneWay" />
                            <Binding Source="{StaticResource ControlDimOpacity}" />
                        </MultiBinding>
                    </husk:Card.Opacity>
                    <husk:Card.Transitions>
                        <Transitions>
                            <BrushTransition Property="Background" Easing="SineEaseOut"
                                             Duration="{StaticResource ControlFasterAnimationDuration}" />
                            <BrushTransition Property="BorderBrush" Easing="SineEaseOut"
                                             Duration="{StaticResource ControlFasterAnimationDuration}" />
                            <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                              Duration="{StaticResource ControlFasterAnimationDuration}" />
                        </Transitions>
                    </husk:Card.Transitions>
                    <Panel>
                        <TextBlock Text="{x:Static lang:Resources.InstancePackageButton_DisabledLabelText}"
                                   FontSize="36" HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   RenderTransform="scaleX(1.2)"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}">
                            <TextBlock.Opacity>
                                <MultiBinding Converter="{x:Static converters:InternalConverters.ZeroOr}">
                                    <Binding Path="IsEnabled" Mode="OneWay" />
                                    <Binding Source="{StaticResource Overlay2Opacity}" />
                                </MultiBinding>
                            </TextBlock.Opacity>
                            <TextBlock.Transitions>
                                <Transitions>
                                    <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                                      Duration="{StaticResource ControlFasterAnimationDuration}" />
                                </Transitions>
                            </TextBlock.Transitions>
                        </TextBlock>
                        <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,*" ColumnSpacing="8">
                            <Border Grid.Row="0" Grid.RowSpan="2" Grid.Column="0"
                                    CornerRadius="{StaticResource SmallCornerRadius}" ClipToBounds="True"
                                    Width="{Binding $self.Bounds.Height}">
                                <Border.Background>
                                    <ImageBrush Source="{Binding Thumbnail}" />
                                </Border.Background>
                            </Border>
                            <Grid Grid.Row="0" Grid.Column="1" ColumnDefinitions="Auto,*,Auto,Auto"
                                  ColumnSpacing="6">
                                <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="4">
                                    <husk:Tag CornerRadius="{StaticResource SmallCornerRadius}"
                                              Classes="Primary" IsVisible="{Binding IsLocked}">
                                        <TextBlock
                                            Text="{x:Static lang:Resources.InstancePackageButton_OriginalTagText}" />
                                    </husk:Tag>
                                    <husk:Tag CornerRadius="{StaticResource SmallCornerRadius}">
                                        <TextBlock
                                            Text="{Binding Kind,Converter={x:Static converters:InternalConverters.LocalizedResourceKindConverter},FallbackValue=Kind}" />
                                    </husk:Tag>
                                </StackPanel>
                                <TextBlock Grid.Column="1"
                                           TextTrimming="CharacterEllipsis"
                                           Text="{Binding ProjectName,FallbackValue=Name}"
                                           FontSize="{StaticResource LargeFontSize}"
                                           FontWeight="{StaticResource ControlStrongFontWeight}"
                                           VerticalAlignment="Center" />
                                <ToggleSwitch Grid.Column="2" OnContent="{x:Null}" OffContent="{x:Null}"
                                              IsChecked="{Binding IsEnabled,Mode=TwoWay}" />
                                <Button Grid.Column="3" Theme="{StaticResource OutlineButtonTheme}" Classes="Small"
                                        Flyout="{TemplateBinding ContextFlyout}">
                                    <icons:PackIconLucide Kind="Ellipsis" Width="{StaticResource MediumFontSize}"
                                                          Height="{StaticResource MediumFontSize}" />
                                </Button>
                            </Grid>
                            <Grid Grid.Row="1" Grid.Column="1" ColumnDefinitions="Auto,Auto,*,Auto"
                                  ColumnSpacing="8">
                                <TextBlock Grid.Column="0" VerticalAlignment="Center"
                                           FontWeight="{StaticResource ControlStrongFontWeight}"
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                           Text="{Binding Author,StringFormat={}@{0},FallbackValue=Author}" />
                                <husk:Divider Grid.Column="1" Orientation="Vertical" Margin="0,4"
                                              IsVisible="{Binding Tags.Count,Converter={x:Static husk:NumberConverters.IsNonZero}}" />
                                <ItemsControl Grid.Column="2"
                                              ItemsSource="{Binding Tags}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate x:DataType="x:String">
                                            <husk:Tag>
                                                <TextBlock Text="{Binding}" />
                                            </husk:Tag>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <StackPanel Orientation="Horizontal" Spacing="4" />
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                </ItemsControl>
                                <ContentControl Grid.Column="3" Content="{Binding Version}" VerticalAlignment="Center">
                                    <ContentControl.DataTemplates>
                                        <DataTemplate DataType="m:InstancePackageVersionModel">
                                            <TextBlock Text="{Binding Name,FallbackValue=Version}"
                                                       FontWeight="{StaticResource ControlStrongFontWeight}" />
                                        </DataTemplate>
                                        <DataTemplate DataType="m:InstancePackageUnspecifiedVersionModel">
                                            <husk:Tag Classes="Primary Small">
                                                <StackPanel Orientation="Horizontal" Spacing="4">
                                                    <TextBlock
                                                        Text="{x:Static lang:Resources.InstancePackageButton_AutoVersionTagText}" />
                                                    <fi:SymbolIcon Symbol="Sparkle"
                                                                   FontSize="{StaticResource SmallFontSize}"
                                                                   VerticalAlignment="Center" />
                                                </StackPanel>
                                            </husk:Tag>
                                        </DataTemplate>
                                    </ContentControl.DataTemplates>
                                </ContentControl>
                            </Grid>
                        </Grid>
                    </Panel>
                </husk:Card>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover /template/ husk|Card#Background">
            <Setter Property="BorderBrush"
                    Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
        </Style>

        <Style Selector="^:pressed /template/ husk|Card#Background">
            <Setter Property="Background"
                    Value="{StaticResource OverlayFullBackgroundBrush}" />
        </Style>

        <Style Selector="^:disabled /template/ husk|Card#Background">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="GridPackageEntryButtonTheme" TargetType="local:InstancePackageButton"
                  BasedOn="{StaticResource BasePackageEntryButtonTheme}">
        <Setter Property="Template">
            <ControlTemplate x:DataType="m:InstancePackageModel">
                <husk:Card Name="Background" Background="{StaticResource LayerBackgroundBrush}"
                           CornerRadius="{StaticResource MediumCornerRadius}">
                    <husk:Card.Opacity>
                        <MultiBinding Converter="{x:Static converters:InternalConverters.OneOr}">
                            <Binding Path="IsEnabled" Mode="OneWay" />
                            <Binding Source="{StaticResource ControlDimOpacity}" />
                        </MultiBinding>
                    </husk:Card.Opacity>
                    <husk:Card.Transitions>
                        <Transitions>
                            <BrushTransition Property="Background" Easing="SineEaseOut"
                                             Duration="{StaticResource ControlFasterAnimationDuration}" />
                            <BrushTransition Property="BorderBrush" Easing="SineEaseOut"
                                             Duration="{StaticResource ControlFasterAnimationDuration}" />
                            <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                              Duration="{StaticResource ControlFasterAnimationDuration}" />
                        </Transitions>
                    </husk:Card.Transitions>
                    <Border CornerRadius="{StaticResource SmallCornerRadius}" ClipToBounds="True"
                            Width="64" Height="64">
                        <Border.Background>
                            <ImageBrush Source="{Binding Thumbnail}" />
                        </Border.Background>
                    </Border>
                </husk:Card>
            </ControlTemplate>
        </Setter>
        <Style Selector="^:pointerover /template/ husk|Card#Background">
            <Setter Property="BorderBrush"
                    Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
        </Style>

        <Style Selector="^:pressed /template/ husk|Card#Background">
            <Setter Property="Background"
                    Value="{StaticResource OverlayFullBackgroundBrush}" />
        </Style>

        <Style Selector="^:disabled /template/ husk|Card#Background">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>