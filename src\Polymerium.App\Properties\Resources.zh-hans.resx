﻿<root>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <data name="MainWindow_BackgroundText" xml:space="preserve">
        <value>Ciallo～(∠・ω&lt; )⌒★</value>
    </data>
    <data name="MainWindow_HomeButtonText" xml:space="preserve">
        <value>主页</value>
    </data>
    <data name="MainWindow_MarketplaceButtonText" xml:space="preserve">
        <value>正在前往：集市</value>
    </data>
    <data name="MainWindow_InstanceFilterPlaceholder" xml:space="preserve">
        <value>过滤实例名...</value>
    </data>
    <data name="MainWindow_AccountButtonText" xml:space="preserve">
        <value>账号</value>
    </data>
    <data name="AccountsView_AddAccountButtonText" xml:space="preserve">
        <value>添加账号</value>
    </data>
    <data name="AccountsView_Title" xml:space="preserve">
        <value>账号</value>
    </data>
    <data name="Shared_EmptyListLabelText" xml:space="preserve">
        <value>空列表</value>
    </data>
    <data name="AccountsView_MarkAsDefaultMenuText" xml:space="preserve">
        <value>标记为默认</value>
    </data>
    <data name="AccountsView_RemoveMenuText" xml:space="preserve">
        <value>移除</value>
    </data>
    <data name="ExceptionView_Title" xml:space="preserve">
        <value>挂壁了...</value>
    </data>
    <data name="ExceptionView_Subtitle" xml:space="preserve">
        <value>程序遇到了无法自救的问题...</value>
    </data>
    <data name="InstanceHomeView_OverviewTitle" xml:space="preserve">
        <value>总览</value>
    </data>
    <data name="InstanceHomeView_PackageCountText" xml:space="preserve">
        <value>个包</value>
    </data>
    <data name="InstanceHomeView_SetupTitle" xml:space="preserve">
        <value>配置</value>
    </data>
    <data name="InstanceHomeView_SetupTypeText" xml:space="preserve">
        <value>类型</value>
    </data>
    <data name="InstanceHomeView_SetupVersionText" xml:space="preserve">
        <value>版本</value>
    </data>
    <data name="InstanceHomeView_ActivitiesTitle" xml:space="preserve">
        <value>活动</value>
    </data>
    <data name="InstanceHomeView_HourCountText" xml:space="preserve">
        <value>总时数</value>
    </data>
    <data name="InstanceHomeView_WidgetsTitle" xml:space="preserve">
        <value>工具</value>
    </data>
    <data name="InstanceHomeView_LaunchPadTitle" xml:space="preserve">
        <value>发射台</value>
    </data>
    <data name="InstanceHomeView_SeeMoreButtonText" xml:space="preserve">
        <value>更多</value>
    </data>
    <data name="InstanceHomeView_SetupEditButtonText" xml:space="preserve">
        <value>编辑</value>
    </data>
    <data name="InstanceHomeView_ReadyLabelText" xml:space="preserve">
        <value>准备就绪</value>
    </data>
    <data name="InstanceHomeView_NoAccountLabelText" xml:space="preserve">
        <value>未选择账号</value>
    </data>
    <data name="InstanceHomeView_LaunchButtonText" xml:space="preserve">
        <value>开始逆行</value>
    </data>
    <data name="InstanceHomeView_AccountButtonTitle" xml:space="preserve">
        <value>账号</value>
    </data>
    <data name="InstanceHomeView_AccountButtonSubtitle" xml:space="preserve">
        <value>未选择</value>
    </data>
    <data name="InstanceHomeView_AbortButtonText" xml:space="preserve">
        <value>不中嘞</value>
    </data>
    <data name="InstanceHomeView_KillButtonText" xml:space="preserve">
        <value>强制退出</value>
    </data>
    <data name="InstanceHomeView_DetachButtonText" xml:space="preserve">
        <value>弹射</value>
    </data>
    <data name="InstanceHomeView_DashboardButtonTitle" xml:space="preserve">
        <value>日志</value>
    </data>
    <data name="InstanceHomeView_DashboardButtonSubtitle" xml:space="preserve">
        <value>信息面板</value>
    </data>
    <data name="InstancePropertiesView_TitleTitle" xml:space="preserve">
        <value>标题</value>
    </data>
    <data name="InstancePropertiesView_TitleSubtitle" xml:space="preserve">
        <value>实例的身份认同</value>
    </data>
    <data name="InstancePropertiesView_DisplayNameLabelText" xml:space="preserve">
        <value>显示名</value>
    </data>
    <data name="InstancePropertiesView_RenameButtonText" xml:space="preserve">
        <value>重命名</value>
    </data>
    <data name="InstancePropertiesView_SelectButtonText" xml:space="preserve">
        <value>选择</value>
    </data>
    <data name="InstancePropertiesView_RemoveButtonText" xml:space="preserve">
        <value>移除</value>
    </data>
    <data name="InstancePropertiesView_OverridesTitle" xml:space="preserve">
        <value>游戏覆盖设置</value>
    </data>
    <data name="InstancePropertiesView_OverridesSubtitle" xml:space="preserve">
        <value>在此处对实例的设置会覆盖全局中对应设置，若干为空，则遵循全局设置</value>
    </data>
    <data name="InstancePropertiesView_NavigateToGlobalButtonTitle" xml:space="preserve">
        <value>跳转到全局游戏设置</value>
    </data>
    <data name="InstancePropertiesView_NavigateToGlobalButtonSubtitle" xml:space="preserve">
        <value>此处的项目会覆盖全局游戏设置</value>
    </data>
    <data name="InstancePropertiesView_JavaLabelText" xml:space="preserve">
        <value>Java 主目录</value>
    </data>
    <data name="InstancePropertiesView_JavaMaxMemoryLabelText" xml:space="preserve">
        <value>Java 最大内存</value>
    </data>
    <data name="InstancePropertiesView_JavaMaxMemoryUnitText" xml:space="preserve">
        <value>兆字节</value>
    </data>
    <data name="InstancePropertiesView_JavaAdditionalArgumentsLabelText" xml:space="preserve">
        <value>Java 附加命令行参数</value>
    </data>
    <data name="InstancePropertiesView_WindowInitialSizeLabelText" xml:space="preserve">
        <value>窗口初始大小</value>
    </data>
    <data name="InstancePropertiesView_WindowHeightLabelText" xml:space="preserve">
        <value>高度</value>
    </data>
    <data name="InstancePropertiesView_WindowWidthLabelText" xml:space="preserve">
        <value>宽度</value>
    </data>
    <data name="InstancePropertiesView_BehaviorsTitle" xml:space="preserve">
        <value>行为</value>
    </data>
    <data name="InstancePropertiesView_BehaviorsSubtitle" xml:space="preserve">
        <value>程序会根据这些设置选择对应策略</value>
    </data>
    <data name="InstancePropertiesView_ResolvePackageDependenciesLabelText" xml:space="preserve">
        <value>[实验] 解析包依赖</value>
    </data>
    <data name="InstancePropertiesView_ResolvePackageDependenciesPrompt" xml:space="preserve">
        <value>由于包托管与分发库在数据上的不完善，开启该选项必定会使包冲突。</value>
    </data>
    <data name="InstancePropertiesView_FastLaunchLabelText" xml:space="preserve">
        <value>快速启动</value>
    </data>
    <data name="InstancePropertiesView_FastLaunchPrompt" xml:space="preserve">
        <value>当成功部署过一次实例后且未检测到文件清单修改时将跳过还原步骤。可能会导致游戏缺少文件。</value>
    </data>
    <data name="InstancePropertiesView_DangerZoneTitle" xml:space="preserve">
        <value>危险操作</value>
    </data>
    <data name="InstancePropertiesView_DangerZoneSubtitle" xml:space="preserve">
        <value>这些设置一旦应用无法恢复</value>
    </data>
    <data name="InstancePropertiesView_SacrificeLabelText" xml:space="preserve">
        <value>牺牲</value>
    </data>
    <data name="InstancePropertiesView_DeleteButtonText" xml:space="preserve">
        <value>删除</value>
    </data>
    <data name="InstancePropertiesView_ResetButtonText" xml:space="preserve">
        <value>重置</value>
    </data>
    <data name="InstancePropertiesView_UnlockButtonText" xml:space="preserve">
        <value>解锁</value>
    </data>
    <data name="InstancePropertiesView_LinkerLabelText" xml:space="preserve">
        <value>关联</value>
    </data>
    <data name="InstancePropertiesView_LinkerPrompt" xml:space="preserve">
        <value>实例会保存并链接关于整合包的引用，以此实现实例的更新，但代价是无法更改/移除整合包带来的包版本。解锁/取消链接实例会失去这些能力和限制。</value>
    </data>
    <data name="InstancePropertiesView_InoperableLabelText" xml:space="preserve">
        <value>实例此时无法操作</value>
    </data>
    <data name="InstanceSetupView_InoperableLabelText" xml:space="preserve">
        <value>操作进行中</value>
    </data>
    <data name="InstanceSetupView_VersionLabelText" xml:space="preserve">
        <value>游戏版本</value>
    </data>
    <data name="InstanceSetupView_LoaderLabelText" xml:space="preserve">
        <value>模组加载器</value>
    </data>
    <data name="InstanceSetupView_ReferenceUnavailableLabelText" xml:space="preserve">
        <value>加载出错</value>
    </data>
    <data name="Shared_ExternalLinkLabelText" xml:space="preserve">
        <value>链接指向</value>
    </data>
    <data name="InstanceSetupView_SwitchVersionButtonText" xml:space="preserve">
        <value>切换版本</value>
    </data>
    <data name="InstanceSetupView_LoadingPackageLabelText" xml:space="preserve">
        <value>加载包信息...</value>
    </data>
    <data name="MarketplacePortalView_Title" xml:space="preserve">
        <value>集市门户</value>
    </data>
    <data name="MarketplacePortalView_NewsLearnMoreButtonText" xml:space="preserve">
        <value>了解更多</value>
    </data>
    <data name="MarketplacePortalView_DiscoveryCenterTitle" xml:space="preserve">
        <value>探索发现</value>
    </data>
    <data name="MarketplacePortalView_DiscoveryCenterSubtitle" xml:space="preserve">
        <value>寻找新的开始</value>
    </data>
    <data name="MarketplacePortalView_SearchButtonText" xml:space="preserve">
        <value>搜索</value>
    </data>
    <data name="MarketplacePortalView_NewsLabelText" xml:space="preserve">
        <value>新闻</value>
    </data>
    <data name="MarketplaceSearchView_Title" xml:space="preserve">
        <value>搜索整合包</value>
    </data>
    <data name="MarketplaceSearchView_SearchBarPlaceholder" xml:space="preserve">
        <value>由这些关键词在...</value>
    </data>
    <data name="MarketplaceSearchView_SearchButtonText" xml:space="preserve">
        <value>搜索</value>
    </data>
    <data name="MarketplaceSearchView_ResultCountLabelText" xml:space="preserve">
        <value>结果：</value>
    </data>
    <data name="MarketplaceSearchView_ResetFilterButtonText" xml:space="preserve">
        <value>清除筛选</value>
    </data>
    <data name="Shared_FetchingLabelText" xml:space="preserve">
        <value>拉取中...</value>
    </data>
    <data name="NewInstanceView_CreateButtonText" xml:space="preserve">
        <value>创建实例</value>
    </data>
    <data name="NewInstanceView_Title" xml:space="preserve">
        <value>新建实例</value>
    </data>
    <data name="NewInstanceView_NameLabelText" xml:space="preserve">
        <value>名称</value>
    </data>
    <data name="NewInstanceView_VersionLabelText" xml:space="preserve">
        <value>版本</value>
    </data>
    <data name="NewInstanceView_PackageCountLabelText" xml:space="preserve">
        <value>包数量</value>
    </data>
    <data name="NewInstanceView_ModLoaderLabelText" xml:space="preserve">
        <value>模组加载器</value>
    </data>
    <data name="NewInstanceView_SeparatorLabelText" xml:space="preserve">
        <value>或来自于</value>
    </data>
    <data name="NewInstanceView_DownloadButtonText" xml:space="preserve">
        <value>集市</value>
    </data>
    <data name="NewInstanceView_ImportButtonText" xml:space="preserve">
        <value>导入</value>
    </data>
    <data name="PackageExplorerView_PendingLabelText" xml:space="preserve">
        <value>待处理</value>
    </data>
    <data name="PackageExplorerView_AddLabelText" xml:space="preserve">
        <value>新增</value>
    </data>
    <data name="PackageExplorerView_ModifyLabelText" xml:space="preserve">
        <value>修改</value>
    </data>
    <data name="PackageExplorerView_RemoveLabelText" xml:space="preserve">
        <value>移除</value>
    </data>
    <data name="PackageExplorerView_EmptyLabelText" xml:space="preserve">
        <value>无</value>
    </data>
    <data name="PackageExplorerView_CollectButtonText" xml:space="preserve">
        <value>执行</value>
    </data>
    <data name="PackageExplorerView_DismissButtonText" xml:space="preserve">
        <value>清空</value>
    </data>
    <data name="PackageExplorerView_SearchBarPlaceholder" xml:space="preserve">
        <value>根据名字...</value>
    </data>
    <data name="PackageExplorerView_SearchButtonText" xml:space="preserve">
        <value>搜索</value>
    </data>
    <data name="SettingsView_Title" xml:space="preserve">
        <value>应用程序设置</value>
    </data>
    <data name="SettingsView_SuperPowerTitle" xml:space="preserve">
        <value>超能力</value>
    </data>
    <data name="SettingsView_SuperPowerSubtitle" xml:space="preserve">
        <value>味大，我为自己带盐</value>
    </data>
    <data name="SettingsView_SuperPowerLabelText" xml:space="preserve">
        <value>激活</value>
    </data>
    <data name="SettingsView_DisplayTitle" xml:space="preserve">
        <value>显示与外观</value>
    </data>
    <data name="SettingsView_DisplaySubtitle" xml:space="preserve">
        <value>效果、字体、语言</value>
    </data>
    <data name="SettingsView_SidebarPlacementLabelText" xml:space="preserve">
        <value>边栏位置</value>
    </data>
    <data name="SettingsView_SidebarPlacementRightText" xml:space="preserve">
        <value>右边</value>
    </data>
    <data name="SettingsView_SidebarPlacementLeftText" xml:space="preserve">
        <value>左边</value>
    </data>
    <data name="SettingsView_ThemeVariantLabelText" xml:space="preserve">
        <value>主题样式</value>
    </data>
    <data name="SettingsView_ThemeVariantSystemText" xml:space="preserve">
        <value>系统</value>
    </data>
    <data name="SettingsView_ThemeVariantLightText" xml:space="preserve">
        <value>亮</value>
    </data>
    <data name="SettingsView_ThemeVariantDarkText" xml:space="preserve">
        <value>暗</value>
    </data>
    <data name="SettingsView_BackgroundStyleLabelText" xml:space="preserve">
        <value>背景风格</value>
    </data>
    <data name="SettingsView_BackgroundStyleAutoText" xml:space="preserve">
        <value>自动</value>
    </data>
    <data name="SettingsView_BackgroundStyleAcrylicText" xml:space="preserve">
        <value>亚克力</value>
    </data>
    <data name="SettingsView_BackgroundStyleMicaText" xml:space="preserve">
        <value>云母</value>
    </data>
    <data name="SettingsView_BackgroundStyleBlurText" xml:space="preserve">
        <value>模糊</value>
    </data>
    <data name="SettingsView_BackgroundStyleNoneText" xml:space="preserve">
        <value>禁用</value>
    </data>
    <data name="SettingsView_LanguageLabelText" xml:space="preserve">
        <value>语言</value>
    </data>
    <data name="SettingsView_FontLabelText" xml:space="preserve">
        <value>字体</value>
    </data>
    <data name="SettingsView_JavaTitle" xml:space="preserve">
        <value>Java 预设</value>
    </data>
    <data name="SettingsView_JavaSubtitle" xml:space="preserve">
        <value>实例会从列表中自动选择最佳版本</value>
    </data>
    <data name="SettingsView_Java11LabelText" xml:space="preserve">
        <value>Java 11 主目录</value>
    </data>
    <data name="SettingsView_Java17LabelText" xml:space="preserve">
        <value>Java 17 主目录</value>
    </data>
    <data name="SettingsView_Java21LabelText" xml:space="preserve">
        <value>Java 21 主目录</value>
    </data>
    <data name="SettingsView_Java8LabelText" xml:space="preserve">
        <value>Java 8 主目录</value>
    </data>
    <data name="SettingsView_GameDefaultsSubtitle" xml:space="preserve">
        <value>此处的配置项可能会被实例单独覆写</value>
    </data>
    <data name="SettingsView_GameDefaultsTitle" xml:space="preserve">
        <value>游戏默认设置</value>
    </data>
    <data name="SettingsView_JavaMaxMemoryLabelText" xml:space="preserve">
        <value>Java 最大内存</value>
    </data>
    <data name="SettingsView_JavaMaxMemoryPlaceholder" xml:space="preserve">
        <value>通常为最大物理内存的一半</value>
    </data>
    <data name="SettingsView_JavaAdditionalArgumentsLabelText" xml:space="preserve">
        <value>Java 附加命令行参数</value>
    </data>
    <data name="SettingsView_JavaAdditionalArgumentsPlaceholder" xml:space="preserve">
        <value>默认为空</value>
    </data>
    <data name="SettingsView_WindowInitialSizeLabelText" xml:space="preserve">
        <value>窗口初始大小</value>
    </data>
    <data name="SettingsView_JavaMaxMemoryUnitText" xml:space="preserve">
        <value>兆字节</value>
    </data>
    <data name="SettingsView_WindowHeightLabelText" xml:space="preserve">
        <value>高度</value>
    </data>
    <data name="SettingsView_WindowWidthLabelText" xml:space="preserve">
        <value>宽度</value>
    </data>
    <data name="AccountCreationMicrosoft_Title" xml:space="preserve">
        <value>微软验证</value>
    </data>
    <data name="AccountCreationMicrosoft_UnavailableLabelText" xml:space="preserve">
        <value>过程出错</value>
    </data>
    <data name="AccountCreationMicrosoft_RetryButtonText" xml:space="preserve">
        <value>重试</value>
    </data>
    <data name="AccountCreationMicrosoft_DoneTitle" xml:space="preserve">
        <value>完成！</value>
    </data>
    <data name="AccountCreationMicrosoft_DoneSubtitle" xml:space="preserve">
        <value>账号链接成功，可以在下一个页面中进行预览。</value>
    </data>
    <data name="AccountCreationMicrosoft_OpenLinkButtonText" xml:space="preserve">
        <value>在浏览器中打开</value>
    </data>
    <data name="AccountCreationMicrosoft_Prompt" xml:space="preserve">
        <value>代码会在十五分钟后过期，请及时在网页中完成认证：</value>
    </data>
    <data name="AccountCreationOffline_Title" xml:space="preserve">
        <value>创建角色</value>
    </data>
    <data name="AccountCreationOffline_NameLabelText" xml:space="preserve">
        <value>名字</value>
    </data>
    <data name="AccountCreationOffline_UuidLabelText" xml:space="preserve">
        <value>U U I D</value>
    </data>
    <data name="AccountCreationOffline_Prompt" xml:space="preserve">
        <value>名字中包含空格或中文字符会导致许多问题。</value>
    </data>
    <data name="AccountCreationPortal_Title" xml:space="preserve">
        <value>选择账号类型</value>
    </data>
    <data name="AccountCreationPortal_MicrosoftTitle" xml:space="preserve">
        <value>微软验证</value>
    </data>
    <data name="AccountCreationPortal_MicrosoftSubtitle" xml:space="preserve">
        <value>视为正版用户并能在在线服务器中使用在线功能。</value>
    </data>
    <data name="AccountCreationPortal_TrialTitle" xml:space="preserve">
        <value>试用</value>
    </data>
    <data name="AccountCreationPortal_TrialSubtitle" xml:space="preserve">
        <value>在未拥有正版账号前使用预设角色体验游戏。</value>
    </data>
    <data name="AccountCreationPortal_OfflineTitle" xml:space="preserve">
        <value>离线</value>
    </data>
    <data name="AccountCreationPortal_OfflineSubtitle" xml:space="preserve">
        <value>不用网。</value>
    </data>
    <data name="AccountCreationPortal_Prompt" xml:space="preserve">
        <value>只有存在已链接的正版账号时才能添加离线账号。</value>
    </data>
    <data name="AccountCreationPreview_Title" xml:space="preserve">
        <value>账号预览</value>
    </data>
    <data name="AccountCreationPreview_Subtitle" xml:space="preserve">
        <value>🎉就差一点了🎉</value>
    </data>
    <data name="AccountCreationTrial_Title" xml:space="preserve">
        <value>挑选你的家庭角色</value>
    </data>
    <data name="ExhibitStatePresenter_AddingTagText" xml:space="preserve">
        <value>新增</value>
    </data>
    <data name="ExhibitStatePresenter_EditableTagText" xml:space="preserve">
        <value>已安装</value>
    </data>
    <data name="ExhibitStatePresenter_LockedTagText" xml:space="preserve">
        <value>锁定</value>
    </data>
    <data name="ExhibitStatePresenter_ModifyingTagText" xml:space="preserve">
        <value>修改</value>
    </data>
    <data name="ExhibitStatePresenter_RemovingTagText" xml:space="preserve">
        <value>移除</value>
    </data>
    <data name="JavaHomeContainer_BrowseButtonText" xml:space="preserve">
        <value>浏览</value>
    </data>
    <data name="JavaHomeContainer_DetectButtonText" xml:space="preserve">
        <value>检测</value>
    </data>
    <data name="JavaHomeContainer_UnknownLabelText" xml:space="preserve">
        <value>未知</value>
    </data>
    <data name="JavaHomeContainer_Prompt" xml:space="preserve">
        <value>使用内置或</value>
    </data>
    <data name="PackageContainer_FilterBarPlaceholder" xml:space="preserve">
        <value>输入过滤字符串...</value>
    </data>
    <data name="PackageContainer_ConditionLabelText" xml:space="preserve">
        <value>条件</value>
    </data>
    <data name="PackageContainer_TagLabelText" xml:space="preserve">
        <value>标签</value>
    </data>
    <data name="ResourceKind_Modpack" xml:space="preserve">
        <value>整合包</value>
    </data>
    <data name="Enum_All" xml:space="preserve">
        <value>全部</value>
    </data>
    <data name="Enum_Disabled" xml:space="preserve">
        <value>禁用</value>
    </data>
    <data name="Enum_Enabled" xml:space="preserve">
        <value>启用</value>
    </data>
    <data name="Enum_None" xml:space="preserve">
        <value>无</value>
    </data>
    <data name="ResourceKind_DataPack" xml:space="preserve">
        <value>数据包</value>
    </data>
    <data name="ResourceKind_Mod" xml:space="preserve">
        <value>模组</value>
    </data>
    <data name="ResourceKind_ResourcePack" xml:space="preserve">
        <value>资源包</value>
    </data>
    <data name="ResourceKind_ShaderPack" xml:space="preserve">
        <value>着色器包</value>
    </data>
    <data name="PackageContainer_SourceOriginalText" xml:space="preserve">
        <value>托管</value>
    </data>
    <data name="PackageContainer_SourceLocalText" xml:space="preserve">
        <value>本地</value>
    </data>
    <data name="PackageContainer_SourceLabelText" xml:space="preserve">
        <value>来源</value>
    </data>
    <data name="PackageContainer_TypeLabelText" xml:space="preserve">
        <value>类型</value>
    </data>
    <data name="PackageContainer_ResultCountLabelText" xml:space="preserve">
        <value>结果</value>
    </data>
    <data name="PackageContainer_BatchUpdateMenuText" xml:space="preserve">
        <value>批量升级</value>
    </data>
    <data name="PackageContainer_GetMoreButtonText" xml:space="preserve">
        <value>获取更多</value>
    </data>
    <data name="PackageContainer_ExportListMenuText" xml:space="preserve">
        <value>导出列表</value>
    </data>
    <data name="SafeLock_Title" xml:space="preserve">
        <value>安全锁</value>
    </data>
    <data name="SafeLock_CodeLabelText" xml:space="preserve">
        <value>代码：</value>
    </data>
    <data name="SafeLock_RepeatLabelText" xml:space="preserve">
        <value>重复</value>
    </data>
    <data name="ExhibitDependencyButton_RequiredTagText" xml:space="preserve">
        <value>必需</value>
    </data>
    <data name="MarketplaceSearchView_OpenWebsiteMenuText" xml:space="preserve">
        <value>前往网站</value>
    </data>
    <data name="ExhibitModpackButton_InstallButtonText" xml:space="preserve">
        <value>安装</value>
    </data>
    <data name="InstanceEntryButton_InstallTagText" xml:space="preserve">
        <value>安装中</value>
    </data>
    <data name="InstanceEntryButton_PreparingTagText" xml:space="preserve">
        <value>准备中</value>
    </data>
    <data name="InstanceEntryButton_UpdatingTagText" xml:space="preserve">
        <value>更新中</value>
    </data>
    <data name="InstanceEntryButton_RunningTagText" xml:space="preserve">
        <value>运行中</value>
    </data>
    <data name="PackageContainer_ActiveMenuText" xml:space="preserve">
        <value>激活</value>
    </data>
    <data name="PackageContainer_OpenWebsiteMenuText" xml:space="preserve">
        <value>前往网站</value>
    </data>
    <data name="PackageContainer_RemoveMenuText" xml:space="preserve">
        <value>移除</value>
    </data>
    <data name="InstancePackageButton_DisabledLabelText" xml:space="preserve">
        <value>禁用</value>
    </data>
    <data name="InstancePackageButton_OriginalTagText" xml:space="preserve">
        <value>导入</value>
    </data>
    <data name="InstancePackageButton_AutoVersionTagText" xml:space="preserve">
        <value>自动版本</value>
    </data>
    <data name="AccountPickerDialog_Title" xml:space="preserve">
        <value>选择一个游戏账号</value>
    </data>
    <data name="AccountPickerDialog_EmptyListPrompt" xml:space="preserve">
        <value>还未添加任何账号</value>
    </data>
    <data name="AccountPickerDialog_ManageAccountsButtonText" xml:space="preserve">
        <value>管理账号</value>
    </data>
    <data name="ExportPackageListDialog_Title" xml:space="preserve">
        <value>导出包列表</value>
    </data>
    <data name="ExportPackageListDialog_Prompt" xml:space="preserve">
        <value>将所有的包保存为表导出到文件</value>
    </data>
    <data name="ExportPackageListDialog_PackageCountLabelText" xml:space="preserve">
        <value>个包</value>
    </data>
    <data name="ExportPackageListDialog_PathLabelText" xml:space="preserve">
        <value>导出到</value>
    </data>
    <data name="ExportPackageListDialog_PathBarPlaceholder" xml:space="preserve">
        <value>选择一个文件路径</value>
    </data>
    <data name="FilePickerDialog_Title" xml:space="preserve">
        <value>选择文件</value>
    </data>
    <data name="FilePickerDialog_DropZonePrompt" xml:space="preserve">
        <value>拖放文件或</value>
    </data>
    <data name="FilePickerDialog_BrowseButtonText" xml:space="preserve">
        <value>浏览文件...</value>
    </data>
    <data name="FilePickerDialog_AlertPrompt" xml:space="preserve">
        <value>过大文件会导致程序崩溃</value>
    </data>
    <data name="FilePickerDialog_PathBarPlaceholder" xml:space="preserve">
        <value>选择文件路径</value>
    </data>
    <data name="GameVersionPickerDialog_Title" xml:space="preserve">
        <value>选择一个游戏版本</value>
    </data>
    <data name="GameVersionPickerDialog_VersionBarPlaceholder" xml:space="preserve">
        <value>输入版本名过滤...</value>
    </data>
    <data name="ReferenceVersionPickerDialog_Title" xml:space="preserve">
        <value>选择更新/降级到特定版本</value>
    </data>
    <data name="ReferenceVersionPickerDialog_Prompt" xml:space="preserve">
        <value>所有模组会被替换，游戏设置会被更新（形容词）的更新（动词）。</value>
    </data>
    <data name="ReleaseType_Release" xml:space="preserve">
        <value>正式</value>
    </data>
    <data name="ReleaseType_Beta" xml:space="preserve">
        <value>B测</value>
    </data>
    <data name="ReleaseType_Alpha" xml:space="preserve">
        <value>A测</value>
    </data>
    <data name="UserInputDialog_Title" xml:space="preserve">
        <value>输入点东西</value>
    </data>
    <data name="UserInputDialog_Prompt" xml:space="preserve">
        <value>总得写点什么东西...</value>
    </data>
    <data name="AccountCreationModal_NextButtonText" xml:space="preserve">
        <value>继续</value>
    </data>
    <data name="AccountCreationModal_FinishButtonText" xml:space="preserve">
        <value>完成</value>
    </data>
    <data name="AccountCreationModal_BackButtonText" xml:space="preserve">
        <value>返回</value>
    </data>
    <data name="AccountCreationModal_DismissButtonText" xml:space="preserve">
        <value>关闭</value>
    </data>
    <data name="AccountEntryModal_LastUsedLabelText" xml:space="preserve">
        <value>最后使用于</value>
    </data>
    <data name="AccountEntryModal_EnrolledLabelText" xml:space="preserve">
        <value>登记于</value>
    </data>
    <data name="AccountEntryModal_OfflinePrompt" xml:space="preserve">
        <value>账号正确，允许启动！</value>
    </data>
    <data name="AccountEntryModal_TrialPrompt" xml:space="preserve">
        <value>账号正确，允许启动！</value>
    </data>
    <data name="AccountEntryModal_MicrosoftPrompt" xml:space="preserve">
        <value>需要定期检查令牌是否有效，检查可以，但定期还没做。</value>
    </data>
    <data name="ExhibitPackageModal_FilterLabelText" xml:space="preserve">
        <value>仅显示兼容版本</value>
    </data>
    <data name="ExhibitPackageModal_VersionBoxPlaceholder" xml:space="preserve">
        <value>指定版本...</value>
    </data>
    <data name="ExhibitPackageModal_VersionLabelText" xml:space="preserve">
        <value>版本：</value>
    </data>
    <data name="ExhibitPackageModal_AddButtonText" xml:space="preserve">
        <value>添加到实例</value>
    </data>
    <data name="ExhibitPackageModal_InstalledVersionTagText" xml:space="preserve">
        <value>已安装：</value>
    </data>
    <data name="ExhibitPackageModal_UnspecifiedVersionTagText" xml:space="preserve">
        <value>未指定</value>
    </data>
    <data name="ExhibitPackageModal_ModifyButtonText" xml:space="preserve">
        <value>应用更改</value>
    </data>
    <data name="ExhibitPackageModal_LockedVersionLabelText" xml:space="preserve">
        <value>版本已锁定</value>
    </data>
    <data name="ExhibitPackageModal_AddingVersionTagText" xml:space="preserve">
        <value>新增：</value>
    </data>
    <data name="ExhibitPackageModal_ModifyingTagText" xml:space="preserve">
        <value>替换：</value>
    </data>
    <data name="ExhibitPackageModal_RemovingTagText" xml:space="preserve">
        <value>移除：</value>
    </data>
    <data name="ExhibitPackageModal_RestoreButtonText" xml:space="preserve">
        <value>还原</value>
    </data>
    <data name="ExhibitPackageModal_AboutTabText" xml:space="preserve">
        <value>关于</value>
    </data>
    <data name="ExhibitPackageModal_DependenciesTabText" xml:space="preserve">
        <value>依赖</value>
    </data>
    <data name="ExhibitPackageModal_DependenciesTabPrompt" xml:space="preserve">
        <value>正在展示如下版本的依赖：</value>
    </data>
    <data name="ExhibitPackageModal_ChangelogsTabText" xml:space="preserve">
        <value>变更日志</value>
    </data>
    <data name="ExhibitPackageModal_ChangelogsTabPrompt" xml:space="preserve">
        <value>正在展示如下版本的日志：</value>
    </data>
    <data name="ExhibitPackageModal_EmptyListLabelText" xml:space="preserve">
        <value>选择一个版本</value>
    </data>
    <data name="InstancePackageModal_VersionsTabText" xml:space="preserve">
        <value>版本</value>
    </data>
    <data name="InstancePackageModal_VersionBoxUnspecificTitle" xml:space="preserve">
        <value>自动选择版本</value>
    </data>
    <data name="InstancePackageModal_VersionBoxUnspecificSubtitle" xml:space="preserve">
        <value>或从列表中手动选择一个版本</value>
    </data>
    <data name="InstancePackageModal_VersionBoxLabelText" xml:space="preserve">
        <value>当前版本</value>
    </data>
    <data name="InstancePackageModal_LockedVersionLabelText" xml:space="preserve">
        <value>不可修改</value>
    </data>
    <data name="InstancePackageModal_FilterLabelText" xml:space="preserve">
        <value>仅显示兼容的版本</value>
    </data>
    <data name="InstancePackageModal_TagsTabText" xml:space="preserve">
        <value>标签</value>
    </data>
    <data name="InstancePackageModal_AddTagButtonText" xml:space="preserve">
        <value>添加</value>
    </data>
    <data name="Dialog_ConfirmButtonText" xml:space="preserve">
        <value>确定</value>
    </data>
    <data name="Dialog_CancelButtonText" xml:space="preserve">
        <value>取消</value>
    </data>
    <data name="Dialog_DismissButtonText" xml:space="preserve">
        <value>关闭</value>
    </data>
    <data name="ExhibitModpackToast_InstallButtonText" xml:space="preserve">
        <value>安装</value>
    </data>
    <data name="JavaHomeContainer_ReqeustJavaTitle" xml:space="preserve">
        <value>选择 Java 可执行文件</value>
    </data>
    <data name="JavaHomeContainer_RequestJavaPrompt" xml:space="preserve">
        <value>文件名形如 bin/java.exe 或 /bin/javaw.exe</value>
    </data>
    <data name="NewInstanceView_RequestFileTitle" xml:space="preserve">
        <value>从文件导入</value>
    </data>
    <data name="NewInstanceView_RequestFilePrompt" xml:space="preserve">
        <value>选择一个包含整合包的压缩文件</value>
    </data>
    <data name="NewInstanceView_ImportDangerNotificationTitle" xml:space="preserve">
        <value>导入失败</value>
    </data>
    <data name="NewInstanceView_IconSavingDangerNotificationTitle" xml:space="preserve">
        <value>写入图标失败</value>
    </data>
    <data name="AccountsView_AccountAddingDangerNotificationPrompt" xml:space="preserve">
        <value>已存在相同 UUID 的账号</value>
    </data>
    <data name="AccountsView_AccountAddingDangerNotificationTitle" xml:space="preserve">
        <value>账号添加失败</value>
    </data>
    <data name="InstanceHomeView_AccountAuthenticationDangerNotificationTitle" xml:space="preserve">
        <value>账号验证失败</value>
    </data>
    <data name="InstanceHomeView_DeployDangerNotificationTitle" xml:space="preserve">
        <value>部署失败</value>
    </data>
    <data name="InstanceHomeView_AccountNotFoundDangerNotificationTitle" xml:space="preserve">
        <value>账号不存在</value>
    </data>
    <data name="InstanceHomeView_AccountNotFoundDangerNotificationPrompt" xml:space="preserve">
        <value>未提供账号或被移除</value>
    </data>
    <data name="InstanceHomeView_AccountNotFoundDangerNotificationSelectActionText" xml:space="preserve">
        <value>选择账号</value>
    </data>
    <data name="InstancePropertiesView_ThumbnailSavingDangerNotificationTitle" xml:space="preserve">
        <value>保存图标失败</value>
    </data>
    <data name="InstancePropertiesView_RequestJavaPrompt" xml:space="preserve">
        <value>文件名形如 /bin/java.exe 或 /bin/javaw.exe</value>
    </data>
    <data name="InstancePropertiesView_RequestJavaTitle" xml:space="preserve">
        <value>选择 Java 可执行文件</value>
    </data>
    <data name="InstancePropertiesView_UnlockingSuccessNotificationPrompt" xml:space="preserve">
        <value>实例不再与任何整合包关联并可以随意编辑。</value>
    </data>
    <data name="InstancePropertiesView_RequestThumbnailTitle" xml:space="preserve">
        <value>选择图标</value>
    </data>
    <data name="InstancePropertiesView_RequestThumbnailPrompt" xml:space="preserve">
        <value>选择一个图像文件</value>
    </data>
    <data name="InstancePropertiesView_ThumbnailSettingDangerNotificationTitle" xml:space="preserve">
        <value>设置实例图标</value>
    </data>
    <data name="InstancePropertiesView_ThumbnailSettingDangerNotificationPrompt" xml:space="preserve">
        <value>选择的文件不是有效图像或无文件可用。</value>
    </data>
    <data name="InstancePropertiesView_RequestNameTitle" xml:space="preserve">
        <value>重命名实例</value>
    </data>
    <data name="InstancePropertiesView_RequestNamePrompt" xml:space="preserve">
        <value>为实例设置一个新名称</value>
    </data>
    <data name="PackageBulkUpdaterModal_UpdateButtonText" xml:space="preserve">
        <value>升级选中</value>
    </data>
    <data name="PackageBulkUpdaterModal_Title" xml:space="preserve">
        <value>批量升级包版本</value>
    </data>
    <data name="PackageContainer_DependencyGraphMenuText" xml:space="preserve">
        <value>依赖关系</value>
    </data>
    <data name="Enum_Off" xml:space="preserve">
        <value>关</value>
    </data>
    <data name="Enum_On" xml:space="preserve">
        <value>开</value>
    </data>
    <data name="InstancePropertiesView_FastLaunchOnText" xml:space="preserve">
        <value>我很急啊</value>
    </data>
    <data name="InstancePropertiesView_ResolvePackageDependenciesOnText" xml:space="preserve">
        <value>我知道我在做什么</value>
    </data>
    <data name="Enum_Vanilla" xml:space="preserve">
        <value>原版</value>
    </data>
    <data name="Enum_Unknown" xml:space="preserve">
        <value>未知</value>
    </data>
    <data name="MainWindow_InstanceInstallingDangerNotificationTitle" xml:space="preserve">
        <value>安装 {0} 失败</value>
    </data>
    <data name="MainWindow_InstanceInstallingSuccessNotificationPrompt" xml:space="preserve">
        <value>实例安装完成</value>
    </data>
    <data name="MainWindow_InstanceInstallingSuccessNotificationOpenText" xml:space="preserve">
        <value>打开</value>
    </data>
    <data name="MainWindow_InstanceUpdatingDangerNotificationTitle" xml:space="preserve">
        <value>更新 {0} 失败</value>
    </data>
    <data name="MainWindow_InstanceUpdatingSuccessNotificationPrompt" xml:space="preserve">
        <value>实例更新完成</value>
    </data>
    <data name="MainWindow_InstanceUpdatingSuccessNotificationOpenText" xml:space="preserve">
        <value>打开</value>
    </data>
    <data name="MainWindow_InstanceDeployingNotificationTitle" xml:space="preserve">
        <value>部署 {0} 失败</value>
    </data>
    <data name="MainWindow_InstanceDeployingSuccessNotificationPrompt" xml:space="preserve">
        <value>实例部署完成</value>
    </data>
    <data name="MainWindow_InstanceLaunchingSuccessNotificationPrompt" xml:space="preserve">
        <value>实例正常退出</value>
    </data>
    <data name="MainWindow_InstanceLaunchingDangerNotificationViewOutputText" xml:space="preserve">
        <value>查看输出</value>
    </data>
    <data name="LaunchMode_Managed" xml:space="preserve">
        <value>托管</value>
    </data>
    <data name="LaunchMode_Debug" xml:space="preserve">
        <value>调试</value>
    </data>
    <data name="LaunchMode_FireAndForget" xml:space="preserve">
        <value>薄情</value>
    </data>
    <data name="SettingsView_TitleBarVisibilityLabelText" xml:space="preserve">
        <value>标题栏可见性</value>
    </data>
    <data name="DeployStage_BuildArtifact" xml:space="preserve">
        <value>构建版本锁...</value>
    </data>
    <data name="DeployStage_CheckArtifact" xml:space="preserve">
        <value>检查版本锁...</value>
    </data>
    <data name="DeployStage_GenerateManifest" xml:space="preserve">
        <value>生成文件清单...</value>
    </data>
    <data name="DeployStage_InstallVanilla" xml:space="preserve">
        <value>安装原版...</value>
    </data>
    <data name="DeployStage_ProcessLoader" xml:space="preserve">
        <value>处理加载器...</value>
    </data>
    <data name="DeployStage_ResolvePackage" xml:space="preserve">
        <value>解析包版本...</value>
    </data>
    <data name="DeployStage_SolidifyManifest" xml:space="preserve">
        <value>固实文件...</value>
    </data>
    <data name="InstanceSetupView_PackageBulkUpdatingProgressingNotificationTitle" xml:space="preserve">
        <value>批量更新包版本</value>
    </data>
    <data name="InstanceSetupView_PackageBulkUpdatingProgressingNotificationPrompt" xml:space="preserve">
        <value>检查版本...({0}/{1})</value>
    </data>
    <data name="InstanceSetupView_PackageBulkUpdatingProgressingNotificationCancelText" xml:space="preserve">
        <value>取消</value>
    </data>
    <data name="InstanceSetupView_PackageBulkUpdatingProgressedNotificationTitle" xml:space="preserve">
        <value>批量更新包版本</value>
    </data>
    <data name="InstanceSetupView_PackageBulkUpdatingProgressedNotificationPrompt" xml:space="preserve">
        <value>{0} 个包存在新版本</value>
    </data>
    <data name="InstanceSetupView_PackageBulkUpdatingProgressedNotificationReviewText" xml:space="preserve">
        <value>查看</value>
    </data>
    <data name="MarketplaceSearchView_ModpackInstallingNotificationPrompt" xml:space="preserve">
        <value>{0} 已添加到队列</value>
    </data>
    <data name="MarketplaceSearchView_ModpackLoadingDangerNotificationTitle" xml:space="preserve">
        <value>获取项目信息失败</value>
    </data>
    <data name="ResourceKind_World" xml:space="preserve">
        <value>存档</value>
    </data>
    <data name="PackageContainer_StatusLabelText" xml:space="preserve">
        <value>状态</value>
    </data>
    <data name="InstancePropertiesView_ThumbnailLabelText" xml:space="preserve">
        <value>图标</value>
    </data>
    <data name="InstancePropertiesView_DebugTitle" xml:space="preserve">
        <value>调试</value>
    </data>
    <data name="InstancePropertiesView_CheckIntegrityButtonText" xml:space="preserve">
        <value>进行一次完整构建</value>
    </data>
    <data name="InstancePropertiesView_DebugSubtitle" xml:space="preserve">
        <value>试试用这些选项解决问题</value>
    </data>
    <data name="InstancePropertiesView_CheckIntegrityLabelText" xml:space="preserve">
        <value>检查完整性</value>
    </data>
    <data name="InstancePackageDependencyButton_RequiredTagText" xml:space="preserve">
        <value>必需</value>
    </data>
    <data name="InstancePackageDependencyButton_RefCountTagText" xml:space="preserve">
        <value>引用：</value>
    </data>
    <data name="InstancePackageModal_DependenciesTabText" xml:space="preserve">
        <value>依赖</value>
    </data>
    <data name="InstancePackageModal_BasicsTabText" xml:space="preserve">
        <value>基本</value>
    </data>
    <data name="SettingsView_AccentColorLabelText" xml:space="preserve">
        <value>强调色</value>
    </data>
    <data name="DeployStage_EnsureRuntime" xml:space="preserve">
        <value>保底运行时...</value>
    </data>
    <data name="MainWindow_PlayMenuText" xml:space="preserve">
        <value>开始</value>
    </data>
    <data name="MainWindow_DeployMenuText" xml:space="preserve">
        <value>部署</value>
    </data>
    <data name="MainWindow_OpenFolderMenuText" xml:space="preserve">
        <value>浏览目录</value>
    </data>
    <data name="MainWindow_SetupMenuText" xml:space="preserve">
        <value>配置</value>
    </data>
    <data name="MainWindow_PropertiesMenuText" xml:space="preserve">
        <value>属性</value>
    </data>
</root>