# Test GitVersion integration
Write-Host "Testing GitVersion integration..." -ForegroundColor Green

# Install GitVersion tool if not already installed
try {
    $gitVersionOutput = dotnet gitversion 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Installing GitVersion tool..." -ForegroundColor Yellow
        dotnet tool install --global GitVersion.Tool
    }
} catch {
    Write-Host "Installing GitVersion tool..." -ForegroundColor Yellow
    dotnet tool install --global GitVersion.Tool
}

# Show current Git status
Write-Host "`nCurrent Git status:" -ForegroundColor Cyan
git status --porcelain
git log --oneline -5

# Run GitVersion to see current version
Write-Host "`nCurrent GitVersion output:" -ForegroundColor Cyan
$versionInfo = dotnet gitversion | ConvertFrom-Json
$versionInfo | Format-List

Write-Host "`nKey version information:" -ForegroundColor Yellow
Write-Host "SemVer: $($versionInfo.SemVer)" -ForegroundColor White
Write-Host "InformationalVersion: $($versionInfo.InformationalVersion)" -ForegroundColor White
Write-Host "PreReleaseTag: $($versionInfo.PreReleaseTag)" -ForegroundColor White
Write-Host "BranchName: $($versionInfo.BranchName)" -ForegroundColor White

# Build the project to see the generated version
Write-Host "`nBuilding project to test version generation..." -ForegroundColor Cyan
dotnet build src/Polymerium.App/Polymerium.App.csproj -c Release

Write-Host "`nVersion integration test completed!" -ForegroundColor Green
Write-Host "The version will be automatically generated based on your Git history and branch." -ForegroundColor Yellow
Write-Host "No manual tags needed - CI will create them automatically!" -ForegroundColor Green
