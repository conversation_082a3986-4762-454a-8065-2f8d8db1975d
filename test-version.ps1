# Test GitVersion integration
Write-Host "Testing GitVersion integration..." -ForegroundColor Green

# Install GitVersion tool if not already installed
try {
    $gitVersionOutput = dotnet gitversion 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Installing GitVersion tool..." -ForegroundColor Yellow
        dotnet tool install --global GitVersion.Tool
    }
} catch {
    Write-Host "Installing GitVersion tool..." -ForegroundColor Yellow
    dotnet tool install --global GitVersion.Tool
}

# Run GitVersion to see current version
Write-Host "`nCurrent GitVersion output:" -ForegroundColor Cyan
dotnet gitversion

# Build the project to see the generated version
Write-Host "`nBuilding project to test version generation..." -ForegroundColor Cyan
dotnet build src/Polymerium.App/Polymerium.App.csproj -c Release

Write-Host "`nVersion integration test completed!" -ForegroundColor Green
Write-Host "Check the build output above to see the generated version numbers." -ForegroundColor Yellow
