﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <Design.PreviewWith>
        <Panel Background="White" Width="256">
            <StackPanel Margin="24" Spacing="12">
                <local:InfoBar Content="Here is some text." />
                <local:InfoBar Header="Hello" Content="Here is some text." />
                <local:InfoBar Header="Hello"
                               Content="Here is some text but very very looooooooooooooooooooooooooooooooong."
                               IsEnabled="False" />
                <local:InfoBar Classes="Primary" Header="Hello" Content="Here is some text." />
                <local:InfoBar Classes="Success" Header="Hello" Content="Here is some text." />
                <local:InfoBar Classes="Warning" Header="Hello" Content="Here is some text." />
                <local:InfoBar Classes="Danger" Header="Hello" Content="Here is some text." />
            </StackPanel>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type local:InfoBar}" TargetType="local:InfoBar">
        <Setter Property="Background" Value="{StaticResource ControlTranslucentFullBackgroundBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlInteractiveBorderBrush}" />
        <Setter Property="BorderThickness" Value="4,1,1,1" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="Padding" Value="12" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Name="Border" Background="{TemplateBinding Background}"
                        CornerRadius="{TemplateBinding CornerRadius}"
                        ClipToBounds="True"
                        BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                    <StackPanel Margin="{TemplateBinding Padding}" Spacing="6">
                        <ContentPresenter DockPanel.Dock="Top" Content="{TemplateBinding Header}"
                                          ContentTemplate="{TemplateBinding HeaderTemplate}"
                                          IsVisible="{TemplateBinding Header, Converter={x:Static ObjectConverters.IsNotNull}}"
                                          FontWeight="{StaticResource ControlStrongFontWeight}" />
                        <ContentPresenter Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}" TextWrapping="Wrap" />
                    </StackPanel>
                </Border>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:disabled /template/ Border#Border">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>

        <Style Selector="^.Primary">
            <Setter Property="BorderBrush" Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
            <Setter Property="Background" Value="{StaticResource ControlAccentTranslucentFullBackgroundBrush}" />
        </Style>
        <Style Selector="^.Success">
            <Setter Property="BorderBrush" Value="{StaticResource ControlSuccessBorderBrush}" />
            <Setter Property="Background" Value="{StaticResource ControlSuccessTranslucentFullBackgroundBrush}" />
        </Style>
        <Style Selector="^.Warning">
            <Setter Property="BorderBrush" Value="{StaticResource ControlWarningBorderBrush}" />
            <Setter Property="Background" Value="{StaticResource ControlWarningTranslucentFullBackgroundBrush}" />
        </Style>
        <Style Selector="^.Danger">
            <Setter Property="BorderBrush" Value="{StaticResource ControlDangerBorderBrush}" />
            <Setter Property="Background" Value="{StaticResource ControlDangerTranslucentFullBackgroundBrush}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>