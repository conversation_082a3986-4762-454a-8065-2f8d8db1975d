﻿<husk:Dialog xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
             xmlns:local="using:Polymerium.App.Dialogs"
             xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             IsPrimaryButtonVisible="True" Title="{x:Static lang:Resources.UserInputDialog_Title}"
             Message="{x:Static lang:Resources.UserInputDialog_Prompt}"
             x:Class="Polymerium.App.Dialogs.UserInputDialog">
    <TextBox Text="{Binding $parent[local:UserInputDialog].Result,Mode=TwoWay}"
             Watermark="{Binding $parent[local:UserInputDialog].Watermark}" />
</husk:Dialog>