﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia">
    <Design.PreviewWith>
        <Panel Background="White">
            <StackPanel Margin="24" Spacing="12">
                <CheckBox>
                    <Button Content="Check or Not" />
                </CheckBox>
                <CheckBox IsEnabled="False">
                    <TextBlock Text="Not" />
                </CheckBox>
                <CheckBox IsChecked="True">
                    <TextBlock Text="Checked" />
                </CheckBox>
                <CheckBox IsThreeState="True" IsChecked="{x:Null}">
                    <TextBlock Text="Or Maybe?" />
                </CheckBox>
            </StackPanel>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type CheckBox}" TargetType="CheckBox">
        <Setter Property="Background" Value="{StaticResource ControlInteractiveBackgroundBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlInteractiveBorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Grid ColumnDefinitions="Auto,*" ColumnSpacing="6" Background="{StaticResource TransparentBrush}">
                    <Border Grid.Column="0" Name="Box" Cursor="Hand"
                            MinHeight="{TemplateBinding FontSize}"
                            MinWidth="{TemplateBinding FontSize}"
                            Height="16"
                            Width="16"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition Property="Background" Easing="SineEaseOut"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}" />
                                <BrushTransition Property="BorderBrush" Easing="SineEaseOut"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </Border.Transitions>
                        <Panel>
                            <fi:SymbolIcon Name="Indeterminate" Symbol="LineHorizontal1"
                                           FontSize="{TemplateBinding FontSize}"
                                           Foreground="{StaticResource ControlDarkForegroundBrush}" Opacity="0"
                                           RenderTransform="scale(0.98)">
                                <fi:SymbolIcon.Transitions>
                                    <Transitions>
                                        <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                                          Duration="{StaticResource ControlFasterAnimationDuration}" />
                                        <TransformOperationsTransition Property="RenderTransform" Easing="BackEaseOut"
                                                                       Duration="{StaticResource ControlFasterAnimationDuration}" />
                                    </Transitions>
                                </fi:SymbolIcon.Transitions>
                            </fi:SymbolIcon>
                            <fi:SymbolIcon Name="Determinate" Symbol="Checkmark" FontSize="{TemplateBinding FontSize}"
                                           Foreground="{StaticResource ControlDarkForegroundBrush}" Opacity="0"
                                           RenderTransform="scale(0.0)">
                                <fi:SymbolIcon.Transitions>
                                    <Transitions>
                                        <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                                          Duration="{StaticResource ControlFasterAnimationDuration}" />
                                        <TransformOperationsTransition Property="RenderTransform" Easing="BackEaseOut"
                                                                       Duration="{StaticResource ControlFasterAnimationDuration}" />
                                    </Transitions>
                                </fi:SymbolIcon.Transitions>
                            </fi:SymbolIcon>
                        </Panel>
                    </Border>
                    <ContentPresenter Name="PART_ContentPresenter"
                                      Grid.Column="1"
                                      Margin="{TemplateBinding Padding}"
                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                      VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                      Content="{TemplateBinding Content}"
                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                      IsVisible="{TemplateBinding Content,
                                                        Converter={x:Static ObjectConverters.IsNotNull}}"
                                      RecognizesAccessKey="True"
                                      TextElement.Foreground="{TemplateBinding Foreground}"
                                      TextElement.FontSize="{TemplateBinding FontSize}"
                                      TextElement.FontWeight="{TemplateBinding FontWeight}" />
                </Grid>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:checked">
            <Style Selector="^ /template/ Border#Box">
                <Setter Property="Background" Value="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
                <Setter Property="BorderBrush" Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
            </Style>

            <Style Selector="^ /template/ fi|SymbolIcon#Determinate">
                <Setter Property="Opacity" Value="1" />
                <Setter Property="RenderTransform" Value="scale(1.0)" />
            </Style>
        </Style>

        <Style Selector="^:unchecked">
            <Style Selector="^:pointerover">
                <Style Selector="^ /template/ Border#Box">
                    <Setter Property="Background" Value="{StaticResource ControlBackgroundBrush}" />
                </Style>
            </Style>
        </Style>

        <Style Selector="^:indeterminate">
            <Style Selector="^ /template/ Border#Box">
                <Setter Property="Background" Value="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
                <Setter Property="BorderBrush" Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
            </Style>

            <Style Selector="^ /template/ fi|SymbolIcon#Indeterminate">
                <Setter Property="Opacity" Value="1" />
                <Setter Property="RenderTransform" Value="scale(1.0)" />
            </Style>
        </Style>

        <Style Selector="^:disabled /template/ Border#Box">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
        <Style Selector="^:disabled /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>

    </ControlTheme>
</ResourceDictionary>