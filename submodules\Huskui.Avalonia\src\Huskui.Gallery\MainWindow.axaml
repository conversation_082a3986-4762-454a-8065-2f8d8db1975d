<husk:AppWindow xmlns="https://github.com/avaloniaui"
                xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                xmlns:local="clr-namespace:Huskui.Gallery"
                xmlns:models="clr-namespace:Huskui.Gallery.Models"
                mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
                ExtendClientAreaTitleBarHeightHint="44"
                TransparencyLevelHint="Mica, None"
                x:Class="Huskui.Gallery.MainWindow"
                Title="Huskui.Gallery" x:DataType="local:MainWindowContext">
    <husk:AppWindow.DataContext>
        <local:MainWindowContext />
    </husk:AppWindow.DataContext>
    <Grid ColumnDefinitions="Auto,*">
        <ListBox Grid.Column="0" ItemsSource="{Binding Entries}"
                 SelectedItem="{Binding SelectedEntry,Mode=OneWayToSource}"
                 Background="{StaticResource ControlTranslucentHalfBackgroundBrush}">
            <ListBox.ItemTemplate>
                <DataTemplate x:DataType="models:EntryModel">
                    <TextBlock Text="{Binding Display}" />
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>
        <husk:Frame Name="Root" Grid.Column="1" />
    </Grid>
</husk:AppWindow>