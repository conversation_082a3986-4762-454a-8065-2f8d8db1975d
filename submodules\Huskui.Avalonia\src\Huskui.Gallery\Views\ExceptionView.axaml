﻿<husk:Page xmlns="https://github.com/avaloniaui"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
           xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
           xmlns:vm="clr-namespace:Huskui.Gallery.ViewModels"
           xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
           mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450" Header="Something went wrong..."
           x:Class="Huskui.Gallery.Views.ExceptionView" x:DataType="vm:ExceptionViewModel">
    <StackPanel Spacing="12">
        <TextBlock Text="{Binding Message}" />
        <Border Background="{StaticResource ControlTranslucentFullBackgroundBrush}" Padding="12">
            <TextBlock Text="{Binding StackTrace}" TextWrapping="Wrap" />
        </Border>
    </StackPanel>
</husk:Page>