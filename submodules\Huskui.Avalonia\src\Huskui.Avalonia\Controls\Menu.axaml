﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:fi="using:FluentIcons.Avalonia"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <Design.PreviewWith>
        <Panel Background="White">
            <StackPanel Margin="24">
                <Menu>
                    <MenuItem Header="Open" ToggleType="Radio" IsChecked="True" />
                    <Separator />
                    <MenuItem Header="Close" ToggleType="CheckBox" IsChecked="True" InputGesture="Ctrl+C" />
                </Menu>
                <Button Content="Hello">
                    <Button.Flyout>
                        <MenuFlyout>
                            <MenuItem Header="-" />
                            <MenuItem Header="Remove" />
                            <MenuItem Header="Dive">
                                <MenuItem.Items>
                                    <MenuItem Header="Into" ToggleType="Radio" />
                                </MenuItem.Items>
                            </MenuItem>
                        </MenuFlyout>
                    </Button.Flyout>
                </Button>
            </StackPanel>
        </Panel>
    </Design.PreviewWith>

    <ControlTheme x:Key="{x:Type Menu}"
                  TargetType="Menu">
        <Setter Property="ItemContainerTheme" Value="{StaticResource TopMenuItemTheme}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                    <ItemsPresenter Name="PART_ItemsPresenter"
                                    ItemsPanel="{TemplateBinding ItemsPanel}"
                                    KeyboardNavigation.TabNavigation="Continue" />
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>

    <ControlTheme x:Key="{x:Type MenuItem}" TargetType="MenuItem">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="Padding" Value="12,8" />
        <Setter Property="ClipToBounds" Value="True" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Name="Border" CornerRadius="{TemplateBinding CornerRadius}"
                        ClipToBounds="{TemplateBinding ClipToBounds}" Margin="2"
                        Background="{TemplateBinding Background}" BackgroundSizing="{TemplateBinding BackgroundSizing}"
                        BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                    <Border.Transitions>
                        <Transitions>
                            <BrushTransition Property="Background" Easing="SineEaseOut"
                                             Duration="{StaticResource ControlFastestAnimationDuration}" />
                        </Transitions>
                    </Border.Transitions>
                    <Panel>
                        <Grid ColumnDefinitions="Auto,10,*,10,Auto" Margin="{TemplateBinding Padding}">
                            <ContentControl Grid.Column="0"
                                            Content="{TemplateBinding Icon}" />
                            <ContentPresenter Grid.Column="2" Name="PART_ContentPresenter"
                                              Content="{TemplateBinding Header}"
                                              ContentTemplate="{TemplateBinding HeaderTemplate}"
                                              RenderTransform="{x:Null}">
                                <ContentPresenter.Transitions>
                                    <Transitions>
                                        <TransformOperationsTransition Property="RenderTransform" Easing="SineEaseOut"
                                                                       Duration="{StaticResource ControlFastestAnimationDuration}" />
                                    </Transitions>
                                </ContentPresenter.Transitions>
                            </ContentPresenter>
                            <StackPanel Grid.Column="4" Orientation="Horizontal" Spacing="6">
                                <Panel>
                                    <TextBlock VerticalAlignment="Center"
                                               Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                               Text="{TemplateBinding InputGesture,Converter={x:Static local:InternalConverters.KeyGestureToString}}" />
                                </Panel>

                                <StackPanel Orientation="Horizontal" Spacing="6">
                                    <fi:SymbolIcon Name="Checkbox" Symbol="CheckboxUnchecked"
                                                   IsVisible="False"
                                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                   FontSize="{TemplateBinding FontSize}"
                                                   HorizontalAlignment="Right" VerticalAlignment="Center" />
                                    <fi:SymbolIcon Name="ExpandMark" Symbol="ArrowRight"
                                                   IsVisible="False"
                                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                   FontSize="{TemplateBinding FontSize}"
                                                   HorizontalAlignment="Right" VerticalAlignment="Center" />
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                        <Popup Name="PART_Popup"
                               IsLightDismissEnabled="False"
                               IsOpen="{TemplateBinding IsSubMenuOpen, Mode=TwoWay}"
                               Placement="RightEdgeAlignedTop">
                            <Border Margin="2,4" Background="{StaticResource OverlaySolidBackgroundBrush}"
                                    CornerRadius="{StaticResource SmallCornerRadius}" ClipToBounds="True">
                                <Border.Effect>
                                    <DropShadowEffect OffsetX="0" OffsetY="0" Opacity="0.2" />
                                </Border.Effect>
                                <ScrollViewer>
                                    <ItemsPresenter Name="PART_ItemsPresenter"
                                                    Grid.IsSharedSizeScope="True"
                                                    ItemsPanel="{TemplateBinding ItemsPanel}" />
                                </ScrollViewer>
                            </Border>
                        </Popup>
                    </Panel>
                </Border>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:separator">
            <Setter Property="Template">
                <ControlTemplate>
                    <Separator />
                </ControlTemplate>
            </Setter>
        </Style>

        <Style Selector="^:selected">
            <Style Selector="^ /template/ Border#Border">
                <Setter Property="Background" Value="{StaticResource OverlayHalfBackgroundBrush}" />
            </Style>

            <Style Selector="^ /template/ ContentPresenter#PART_ContentPresenter">
                <Setter Property="RenderTransform" Value="translateX(8px)" />
            </Style>
        </Style>

        <Style Selector="^:not(:empty) /template/ fi|SymbolIcon#ExpandMark">
            <Setter Property="IsVisible" Value="True" />
        </Style>

        <Style Selector="^:toggle /template/ fi|SymbolIcon#Checkbox">
            <Setter Property="IsVisible" Value="True" />
        </Style>

        <Style Selector="^:radio /template/ fi|SymbolIcon#Checkbox">
            <Setter Property="IsVisible" Value="True" />
            <Setter Property="Symbol" Value="RadioButton" />
        </Style>

        <Style Selector="^:checked:toggle /template/ fi|SymbolIcon#Checkbox">
            <Setter Property="Symbol" Value="CheckboxChecked" />
            <Setter Property="IconVariant" Value="Filled" />
        </Style>

        <Style Selector="^:checked:radio /template/ fi|SymbolIcon#Checkbox">
            <Setter Property="IconVariant" Value="Filled" />
        </Style>

        <Style Selector="^:disabled /template/ Border#Border">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="TopMenuItemTheme" TargetType="MenuItem">
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="Padding" Value="12,8" />
        <Setter Property="ClipToBounds" Value="True" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Name="Border" CornerRadius="{TemplateBinding CornerRadius}"
                        ClipToBounds="{TemplateBinding ClipToBounds}" Margin="2"
                        Background="{TemplateBinding Background}" BackgroundSizing="{TemplateBinding BackgroundSizing}"
                        BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                    <Border.Transitions>
                        <Transitions>
                            <BrushTransition Property="Background" Easing="SineEaseOut"
                                             Duration="{StaticResource ControlFastestAnimationDuration}" />
                        </Transitions>
                    </Border.Transitions>
                    <Panel>
                        <Grid ColumnDefinitions="*,4,Auto,4,Auto" Margin="{TemplateBinding Padding}">
                            <ContentPresenter Grid.Column="0" Name="PART_ContentPresenter"
                                              Content="{TemplateBinding Header}"
                                              ContentTemplate="{TemplateBinding HeaderTemplate}"
                                              RenderTransform="{x:Null}">
                                <ContentPresenter.Transitions>
                                    <Transitions>
                                        <TransformOperationsTransition Property="RenderTransform" Easing="SineEaseOut"
                                                                       Duration="{StaticResource ControlFastestAnimationDuration}" />
                                    </Transitions>
                                </ContentPresenter.Transitions>
                            </ContentPresenter>

                            <Panel Grid.Column="2">
                                <TextBlock x:Name="PART_InputGestureText" VerticalAlignment="Center"
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                           Text="{TemplateBinding InputGesture,Converter={x:Static local:InternalConverters.KeyGestureToString}}" />
                            </Panel>

                            <Panel Grid.Column="4">
                                <fi:SymbolIcon Name="ExpandMark" Symbol="ArrowRight"
                                               IsVisible="False"
                                               Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                               FontSize="{TemplateBinding FontSize}"
                                               HorizontalAlignment="Right" VerticalAlignment="Center" />
                                <fi:SymbolIcon Name="Checkbox" Symbol="CheckboxUnchecked"
                                               IsVisible="False"
                                               Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                               FontSize="{TemplateBinding FontSize}"
                                               HorizontalAlignment="Right" VerticalAlignment="Center" />
                            </Panel>
                        </Grid>
                        <Popup Name="PART_Popup"
                               IsLightDismissEnabled="False"
                               IsOpen="{TemplateBinding IsSubMenuOpen, Mode=TwoWay}"
                               Placement="RightEdgeAlignedTop"
                               OverlayInputPassThroughElement="{Binding $parent[Menu]}">
                            <Border Margin="2,4" Background="{StaticResource OverlaySolidBackgroundBrush}"
                                    CornerRadius="{StaticResource SmallCornerRadius}" ClipToBounds="True">
                                <Border.Effect>
                                    <DropShadowEffect OffsetX="0" OffsetY="0" Opacity="0.2" />
                                </Border.Effect>
                                <ScrollViewer>
                                    <ItemsPresenter Name="PART_ItemsPresenter"
                                                    Grid.IsSharedSizeScope="True"
                                                    ItemsPanel="{TemplateBinding ItemsPanel}" />
                                </ScrollViewer>
                            </Border>
                        </Popup>
                    </Panel>
                </Border>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:separator">
            <Setter Property="Template">
                <ControlTemplate>
                    <Separator Margin="4" />
                </ControlTemplate>
            </Setter>
        </Style>

        <Style Selector="^:selected">
            <Style Selector="^ /template/ Border#Border">
                <Setter Property="Background" Value="{StaticResource OverlayHalfBackgroundBrush}" />
            </Style>
        </Style>

        <Style Selector="^:pressed">
            <Style Selector="^ /template/ Border#Border">
                <Setter Property="Background" Value="{StaticResource OverlayFullBackgroundBrush}" />
            </Style>
        </Style>

        <Style Selector="^:not(:empty) /template/ fi|SymbolIcon#ExpandMark">
            <Setter Property="IsVisible" Value="True" />
        </Style>

        <Style Selector="^:toggle /template/ fi|SymbolIcon#Checkbox">
            <Setter Property="IsVisible" Value="True" />
        </Style>

        <Style Selector="^:radio /template/ fi|SymbolIcon#Checkbox">
            <Setter Property="IsVisible" Value="True" />
            <Setter Property="Symbol" Value="RadioButton" />
        </Style>

        <Style Selector="^:checked:toggle /template/ fi|SymbolIcon#Checkbox">
            <Setter Property="Symbol" Value="CheckboxChecked" />
            <Setter Property="IconVariant" Value="Filled" />
        </Style>

        <Style Selector="^:checked:radio /template/ fi|SymbolIcon#Checkbox">
            <Setter Property="IconVariant" Value="Filled" />
        </Style>

        <Style Selector="^:disabled /template/ Border#Border">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>