﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:Huskui.Gallery.Controls"
                    xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia">
    <ControlTheme x:Key="{x:Type controls:ControlPresenter}" TargetType="controls:ControlPresenter">
        <Setter Property="Padding" Value="64" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlBorderBrush}" />
        <Setter Property="Background" Value="{StaticResource OverlaySolidBackgroundBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource MediumCornerRadius}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border CornerRadius="{TemplateBinding CornerRadius}" BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Background="{TemplateBinding Background}">
                    <Grid RowDefinitions="Auto,Auto,*">
                        <StackPanel Grid.Row="0" Margin="12">
                            <TextBlock Text="{TemplateBinding Title}" FontSize="{StaticResource LargeFontSize}" />
                            <TextBlock Text="{TemplateBinding Subtitle}"
                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                       FontSize="{StaticResource SmallFontSize}"
                                       IsVisible="{TemplateBinding Subtitle, Converter={x:Static StringConverters.IsNotNullOrEmpty}}" />
                        </StackPanel>
                        <husk:Divider Grid.Row="1" />
                        <Grid Grid.Row="2" ColumnDefinitions="*,Auto,Auto">
                            <ContentPresenter Grid.Column="0"
                                              HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              Padding="{TemplateBinding Padding}" />
                            <husk:Divider Grid.Column="1" Orientation="Vertical"
                                          IsVisible="{TemplateBinding Operation,Converter={x:Static ObjectConverters.IsNotNull}}" />
                            <ContentPresenter Grid.Column="2" Content="{TemplateBinding Operation}"
                                              IsVisible="{TemplateBinding Operation,Converter={x:Static ObjectConverters.IsNotNull}}"
                                              Padding="12" />
                        </Grid>
                    </Grid>
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>