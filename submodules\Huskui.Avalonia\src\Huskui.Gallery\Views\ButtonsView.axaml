﻿<husk:Page xmlns="https://github.com/avaloniaui"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
           xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
           xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
           xmlns:controls="clr-namespace:Huskui.Gallery.Controls"
           mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
           x:Class="Huskui.Gallery.Views.ButtonsView" Header="Buttons">
    <StackPanel Spacing="32">
        <controls:ControlPresenter Title="Basic Button Types">
            <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto" ColumnSpacing="12"
                  RowSpacing="12">
                <Button Grid.Column="0" Grid.Row="0" Content="Default" />
                <Button Grid.Column="0" Grid.Row="1" Content="Default.Primary" Classes="Primary" />
                <Button Grid.Column="0" Grid.Row="2" Content="Default.Success" Classes="Success" />
                <Button Grid.Column="0" Grid.Row="3" Content="Default.Warning" Classes="Warning" />
                <Button Grid.Column="0" Grid.Row="4" Content="Default.Danger" Classes="Danger" />
                <Button Grid.Column="0" Grid.Row="5" Content="Default:disabled" IsEnabled="False" />
                <Button Grid.Column="1" Grid.Row="0" Content="OutlineButtonTheme"
                        Theme="{StaticResource OutlineButtonTheme}" />
                <Button Grid.Column="1" Grid.Row="1" Content="OutlineButtonTheme.Primary" Classes="Primary"
                        Theme="{StaticResource OutlineButtonTheme}" />
                <Button Grid.Column="1" Grid.Row="2" Content="OutlineButtonTheme.Success" Classes="Success"
                        Theme="{StaticResource OutlineButtonTheme}" />
                <Button Grid.Column="1" Grid.Row="3" Content="OutlineButtonTheme.Warning" Classes="Warning"
                        Theme="{StaticResource OutlineButtonTheme}" />
                <Button Grid.Column="1" Grid.Row="4" Content="OutlineButtonTheme.Danger" Classes="Danger"
                        Theme="{StaticResource OutlineButtonTheme}" />
                <Button Grid.Column="1" Grid.Row="5" Content="OutlineButtonTheme:disabled" IsEnabled="False"
                        Theme="{StaticResource OutlineButtonTheme}" />
                <Button Grid.Column="2" Grid.Row="0" Content="GhostButtonTheme"
                        Theme="{StaticResource GhostButtonTheme}" />
                <Button Grid.Column="2" Grid.Row="1" Content="GhostButtonTheme.Primary" Classes="Primary"
                        Theme="{StaticResource GhostButtonTheme}" />
                <Button Grid.Column="2" Grid.Row="2" Content="GhostButtonTheme.Success" Classes="Success"
                        Theme="{StaticResource GhostButtonTheme}" />
                <Button Grid.Column="2" Grid.Row="3" Content="GhostButtonTheme.Warning" Classes="Warning"
                        Theme="{StaticResource GhostButtonTheme}" />
                <Button Grid.Column="2" Grid.Row="4" Content="GhostButtonTheme.Danger" Classes="Danger"
                        Theme="{StaticResource GhostButtonTheme}" />
                <Button Grid.Column="2" Grid.Row="5" Content="GhostButtonTheme:disabled" IsEnabled="False"
                        Theme="{StaticResource GhostButtonTheme}" />
            </Grid>
        </controls:ControlPresenter>
        <controls:ControlPresenter Title="SplitButton &amp; DropDownButton">
            <StackPanel Spacing="12" Orientation="Horizontal">
                <SplitButton Content="SplitButton">
                    <SplitButton.Flyout>
                        <MenuFlyout>
                            <MenuItem Header="Other Button" />
                        </MenuFlyout>
                    </SplitButton.Flyout>
                </SplitButton>
                <DropDownButton Content="DropDownButton">
                    <DropDownButton.Flyout>
                        <MenuFlyout>
                            <MenuItem Header="Other Button" />
                        </MenuFlyout>
                    </DropDownButton.Flyout>
                </DropDownButton>
            </StackPanel>
        </controls:ControlPresenter>
        <controls:ControlPresenter Title="ToggleButton &amp; ToggleSplitButton">
            <StackPanel Spacing="12">
                <ToggleButton Content="ToggleButton" />
                <ToggleSplitButton Content="ToggleSplitButton">
                    <ToggleSplitButton.Flyout>
                        <MenuFlyout>
                            <MenuItem Header="Other Button" />
                        </MenuFlyout>
                    </ToggleSplitButton.Flyout>
                </ToggleSplitButton>
            </StackPanel>
        </controls:ControlPresenter>
    </StackPanel>
</husk:Page>