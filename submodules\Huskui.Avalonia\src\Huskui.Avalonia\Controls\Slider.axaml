﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Design.PreviewWith>
        <Panel Background="White">
            <StackPanel Margin="24" Spacing="12">
                <Slider Width="128" Value="50" />
                <Slider Orientation="Vertical" Height="128" Value="50" />
            </StackPanel>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type Slider}" TargetType="Slider">
        <Setter Property="Padding" Value="2" />
        <Setter Property="CornerRadius" Value="{StaticResource FullCornerRadius}" />
        <Style Selector="^:horizontal">
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="Template">
                <ControlTemplate>
                    <Panel Name="Container">
                        <Track Margin="{TemplateBinding Padding}"
                               Name="PART_Track"
                               IsDirectionReversed="{TemplateBinding IsDirectionReversed}"
                               Maximum="{TemplateBinding Maximum}"
                               Minimum="{TemplateBinding Minimum}"
                               Orientation="Horizontal"
                               Value="{TemplateBinding Value, Mode=TwoWay}">
                            <Track.DecreaseButton>
                                <RepeatButton
                                    Name="PART_DecreaseButton"
                                    Focusable="False"
                                    Height="6"
                                    Margin="0,0,-4,0"
                                    CornerRadius="{TemplateBinding CornerRadius}"
                                    Background="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
                            </Track.DecreaseButton>
                            <Track.IncreaseButton>
                                <RepeatButton
                                    Name="PART_IncreaseButton"
                                    Focusable="False"
                                    Height="6"
                                    Margin="-4,0,0,0"
                                    CornerRadius="{TemplateBinding CornerRadius}"
                                    Background="{StaticResource ControlBackgroundBrush}" />
                            </Track.IncreaseButton>
                            <Thumb Width="18" Height="18" />
                        </Track>
                    </Panel>
                </ControlTemplate>
            </Setter>
        </Style>
        <Style Selector="^:vertical">
            <Setter Property="HorizontalAlignment" Value="Center" />
            <Setter Property="Template">
                <ControlTemplate>
                    <Panel Name="Container">
                        <Track Margin="{TemplateBinding Padding}"
                               Name="PART_Track"
                               IsDirectionReversed="{TemplateBinding IsDirectionReversed}"
                               Maximum="{TemplateBinding Maximum}"
                               Minimum="{TemplateBinding Minimum}"
                               Orientation="Vertical"
                               Value="{TemplateBinding Value, Mode=TwoWay}">
                            <Track.DecreaseButton>
                                <RepeatButton
                                    Name="PART_DecreaseButton"
                                    Focusable="False"
                                    Width="6"
                                    Margin="0,-4,0,0"
                                    CornerRadius="{TemplateBinding CornerRadius}"
                                    Background="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
                            </Track.DecreaseButton>
                            <Track.IncreaseButton>
                                <RepeatButton
                                    Name="PART_IncreaseButton"
                                    Focusable="False"
                                    Width="6"
                                    Margin="0,0,0,-4"
                                    CornerRadius="{TemplateBinding CornerRadius}"
                                    Background="{StaticResource ControlBackgroundBrush}" />
                            </Track.IncreaseButton>
                            <Thumb Width="18" Height="18" />
                        </Track>
                    </Panel>
                </ControlTemplate>
            </Setter>
        </Style>

        <Style Selector="^:disabled">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>

        <Style Selector="^ /template/ RepeatButton">
            <Setter Property="Template">
                <ControlTemplate>
                    <Border
                        Margin="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        CornerRadius="{TemplateBinding CornerRadius}" />
                </ControlTemplate>
            </Setter>
        </Style>

        <Style Selector="^ /template/ Thumb">
            <Setter Property="Theme">
                <ControlTheme TargetType="Thumb">
                    <Setter Property="CornerRadius" Value="{StaticResource FullCornerRadius}" />
                    <Setter Property="Background" Value="{StaticResource OverlaySolidBackgroundBrush}" />
                    <Setter Property="Padding" Value="3" />
                    <Setter Property="ClipToBounds" Value="False" />
                    <Setter Property="Template">
                        <ControlTemplate>
                            <Border BorderThickness="{TemplateBinding BorderThickness}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    CornerRadius="{TemplateBinding CornerRadius}"
                                    Background="{TemplateBinding Background}" Padding="{TemplateBinding Padding}"
                                    BoxShadow="0 0 2 0 #3f000000">
                                <Ellipse Name="Knob" Fill="{StaticResource ControlAccentInteractiveBackgroundBrush}">
                                    <Ellipse.Transitions>
                                        <Transitions>
                                            <TransformOperationsTransition Property="RenderTransform"
                                                                           Easing="CubicEaseOut"
                                                                           Duration="{StaticResource ControlInstantAnimationDuration}" />
                                        </Transitions>
                                    </Ellipse.Transitions>
                                </Ellipse>
                            </Border>
                        </ControlTemplate>
                    </Setter>

                    <Style Selector="^:pointerover /template/ Ellipse#Knob">
                        <Setter Property="RenderTransform" Value="scale(1.1)" />
                    </Style>
                    <Style Selector="^:pressed /template/ Ellipse#Knob">
                        <Setter Property="RenderTransform" Value="scale(0.9)" />
                    </Style>
                </ControlTheme>
            </Setter>
        </Style>

        <Style Selector="^:disabled /template/ Panel#Container">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>